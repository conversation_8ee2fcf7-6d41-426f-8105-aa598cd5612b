<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终极修复版 v2.3 - 完整面板同步</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .header {
            background: white;
            border-bottom: 1px solid #ddd;
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 50px;
        }
        .title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        .toolbar {
            display: flex;
            gap: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
            white-space: nowrap;
        }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #1e7e34; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        .main-container {
            flex: 1;
            display: flex;
            background: white;
            margin-top: 50px;
            overflow: hidden;
            height: calc(100vh - 50px);
        }
        .panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #ddd;
        }
        .panel:last-child {
            border-right: none;
        }
        .panel-header {
            background: #f8f9fa;
            padding: 6px 12px;
            border-bottom: 1px solid #ddd;
            font-size: 13px;
            font-weight: 500;
            color: #495057;
        }
        .panel-content {
            flex: 1;
            padding: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .text-input {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            font-size: 13px;
            line-height: 1.5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            resize: none;
            outline: none;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }
        .text-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .text-output {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            font-size: 13px;
            line-height: 1.5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f9f9f9;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }
        .highlight-symbol {
            background: #ffff00;
            color: #ff0000;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 2px;
        }
        .highlight-sensitive {
            background: #ff5722;
            color: white;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 3px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .sensitive-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            display: none;
        }

        .sensitive-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #856404;
            font-size: 12px;
        }

        .sensitive-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .sensitive-list {
            max-height: 120px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 6px;
            background: #f9f9f9;
            font-size: 11px;
        }

        .sensitive-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2px 4px;
            margin-bottom: 2px;
            background: white;
            border-radius: 2px;
            font-size: 11px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 1px 4px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
        }

        .stats-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-size: 11px;
            display: none;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .category-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 11px;
        }

        .category-toggle input {
            margin-right: 6px;
            transform: scale(0.8);
        }

        .function-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            display: none;
        }

        .function-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #495057;
            font-size: 12px;
        }

        .function-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .function-textarea {
            width: 100%;
            height: 80px;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 6px;
            font-size: 11px;
            resize: vertical;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        /* 导出弹窗特殊样式 */
        #exportWordModal .modal-content {
            width: 50%;
            max-width: 600px;
        }

        #exportWordModal .function-input {
            margin-bottom: 8px;
            padding: 8px 12px;
            font-size: 14px;
        }

        #exportWordModal label {
            color: #495057;
            font-weight: 500;
            font-size: 14px;
        }

        #exportPreview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
        }

        .export-option-group {
            display: flex;
            gap: 15px;
            margin-top: 8px;
        }

        .export-checkbox-label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            cursor: pointer;
            padding: 5px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .export-checkbox-label:hover {
            background-color: #f8f9fa;
        }

        .export-checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        .split-result {
            max-height: 120px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 6px;
            background: white;
            font-size: 11px;
            margin-bottom: 6px;
        }

        .split-item {
            padding: 2px 4px;
            margin-bottom: 2px;
            background: #f8f9fa;
            border-radius: 2px;
            border-left: 3px solid #007bff;
        }

        .quick-check-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 8px;
            margin-top: 6px;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
        }

        .check-summary {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 3px;
            padding: 6px;
            margin-bottom: 8px;
            font-size: 10px;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .check-status {
            font-weight: bold;
        }

        .check-ok { color: #28a745; }
        .check-warning { color: #ffc107; }
        .check-error { color: #dc3545; }

        .marked-blocks {
            margin-top: 8px;
        }

        .block-category {
            margin-bottom: 8px;
        }

        .category-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 4px;
            font-size: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 2px;
        }

        .block-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 2px;
            padding: 4px 6px;
            margin-bottom: 2px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 10px;
            line-height: 1.3;
        }

        .block-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .block-item.number-block {
            border-left: 3px solid #ffc107;
        }

        .block-item.dialog-block {
            border-left: 3px solid #ff9800;
        }

        .block-item.sensitive-block {
            border-left: 3px solid #f44336;
        }

        .block-item.english-block {
            border-left: 3px solid #9c27b0;
        }

        .block-preview {
            color: #666;
            font-style: italic;
            margin-top: 2px;
        }

        .block-position {
            color: #999;
            font-size: 9px;
            float: right;
        }

        /* 通用弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        /* 快速检查弹窗特殊样式 - 无背景遮罩 */
        #quickCheckModal {
            background-color: transparent;
            pointer-events: none; /* 让背景不阻止点击 */
        }

        #quickCheckModal .modal-content {
            pointer-events: auto; /* 恢复弹窗内容的点击 */
            transition: all 0.3s ease;
            animation: slideInFromRight 0.3s ease;
        }

        @keyframes slideInFromRight {
            from {
                transform: translateX(100px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 快速检查项的优化样式 */
        .quick-check-item {
            will-change: transform, background-color, border-color;
        }

        /* 高亮动画 */
        @keyframes highlightPulse {
            0% {
                background: rgba(255, 182, 193, 0.8);
                transform: scale(1);
            }
            50% {
                background: rgba(255, 182, 193, 0.95);
                transform: scale(1.02);
            }
            100% {
                background: rgba(255, 182, 193, 0.8);
                transform: scale(1);
            }
        }

        /* 句子高亮样式 */
        .sentence-highlight {
            position: relative;
            z-index: 10;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(255,182,193,0.3);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 4px 16px rgba(255,182,193,0.6);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(255,182,193,0.3);
            }
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 6px;
            width: 45%;
            max-width: 500px;
            max-height: 65%;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 18px;
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .modal-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            padding: 0;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 15px;
            flex: 1;
            overflow-y: auto;
            max-height: calc(65vh - 120px);
        }

        .summary-grid {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .summary-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .summary-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .summary-label {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .summary-number.total { color: #495057; }
        .summary-number.ellipsis { color: #fd7e14; }
        .summary-number.exclamation { color: #dc3545; }
        .summary-number.colon { color: #6f42c1; }

        .issues-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .issue-category {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            transition: all 0.2s ease;
        }

        .issue-category:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }

        .category-header {
            padding: 8px 12px;
            font-weight: 600;
            font-size: 13px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .category-header.ellipsis {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }
        .category-header.exclamation {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        .category-header.colon {
            background: linear-gradient(135deg, #e2e3f3 0%, #d6d8e5 100%);
            color: #4a4a7a;
        }

        .issue-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .issue-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .issue-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(5px);
        }

        .issue-item:last-child {
            border-bottom: none;
        }

        .issue-content {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 12px;
            color: #495057;
        }

        .issue-preview {
            font-size: 11px;
            color: #6c757d;
            line-height: 1.4;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 4px 0;
            border-left: 2px solid #dee2e6;
        }

        .issue-location {
            font-size: 10px;
            color: #adb5bd;
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .modal-footer {
            padding: 10px 15px;
            border-top: 1px solid #dee2e6;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0 0 6px 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 -1px 5px rgba(0,0,0,0.05);
        }

        .modal-footer-info {
            font-size: 11px;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .modal-footer-buttons {
            display: flex;
            gap: 8px;
        }

        .modal-footer-buttons .btn {
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-footer-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 脉冲动画效果 */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 闪烁动画效果 */
        @keyframes blink {
            0% {
                background: #ff4757;
                transform: scale(1);
            }
            100% {
                background: #ff6b7a;
                transform: scale(1.05);
            }
        }

        /* Word风格选中效果 */
        .word-selected {
            background-color: #c7c7c7 !important;
            color: #000 !important;
            padding: 4px 8px !important;
            margin: 2px 0 !important;
            border-radius: 2px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            outline: 1px solid #0078d4 !important;
            outline-offset: 1px !important;
            transition: all 0.2s ease !important;
        }

        .word-selected:focus {
            outline: 2px solid #0078d4 !important;
            outline-offset: 1px !important;
            box-shadow: 0 2px 6px rgba(0,120,212,0.2) !important;
        }

        .info-panel {
            display: none;
        }
        .debug-log {
            display: none;
        }

        .sensitive-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }

        .sensitive-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #856404;
            font-size: 12px;
        }

        .sensitive-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .sensitive-list {
            max-height: 120px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 6px;
            background: #f9f9f9;
            font-size: 11px;
        }

        .sensitive-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2px 4px;
            margin-bottom: 2px;
            background: white;
            border-radius: 2px;
            font-size: 11px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 1px 4px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
        }

        .stats-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-size: 11px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">终极修复版 - 彻底解决句末符号问题</div>
        <div class="toolbar">
            <button class="btn success" onclick="processText()">🔧 处理文本</button>
            <button class="btn" onclick="copyResult()">📋 复制输出</button>
            <button class="btn" onclick="copyOriginal()">📄 复制原文</button>
            <button class="btn warning" onclick="openModal('findReplaceModal')">🔍 查找替换</button>
            <button class="btn" onclick="openModal('splitTextModal')">✂️ 拆分文本</button>
            <button class="btn" onclick="openModal('quickCheckModal')">🔍 快速检查</button>
            <button class="btn" onclick="openModal('sensitiveWordsModal')">🛡️ 敏感词管理</button>
            <button class="btn" onclick="openExportWordModal()">📄 导出文本</button>
            <button class="btn danger" onclick="clearAll()">🗑️ 清空</button>
        </div>


    </div>
    
    <div class="main-container">
        <div class="panel">
            <div class="panel-header">
                原始文本
                <span id="originalTextCount" style="float: right; font-size: 11px; color: #666; font-weight: normal;">0 字符</span>
            </div>
            <div class="panel-content">
                <textarea id="originalText" class="text-input" placeholder="在此输入或粘贴文本内容..."></textarea>
                <div class="info-panel">
                    <strong>终极修复规则：</strong><br>
                    🗑️ 删除：逗号，句号。对话符「」英文省略号...<br>
                    ✅ 保留：问号？感叹号！冒号：省略号…<br>

                    � 新规则：去掉对话符，保留冒号和省略号<br>
                    📝 自动：粘贴后自动处理，输入后300ms自动处理
                </div>
                <div class="debug-log" id="debugLog">调试日志...</div>

                <div class="sensitive-panel" id="sensitivePanel" style="display: none;">
                    <div class="sensitive-title">🔍 敏感词管理</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; margin-bottom: 6px;">
                        <input type="text" class="sensitive-input" id="newSensitiveWord" placeholder="敏感词" style="margin-bottom: 0;">
                        <input type="text" class="sensitive-input" id="newReplacement" placeholder="替换词(可选)" style="margin-bottom: 0;">
                    </div>
                    <div style="font-size: 10px; color: #666; margin-bottom: 6px;">💡 提示：不填替换词则只高亮显示，不进行替换</div>
                    <button class="btn" style="width: 100%; margin-bottom: 6px; font-size: 11px;" onclick="addCustomWord()">添加敏感词</button>
                    <div class="sensitive-list" id="sensitiveWordsList"></div>
                    <button class="btn" style="width: 100%; margin-top: 6px; font-size: 11px;" onclick="resetDefaultSensitive()">恢复默认</button>

                    <div style="margin-top: 10px;">
                        <div class="sensitive-title">⚙️ 分类设置</div>
                        <div id="categoryToggles"></div>
                    </div>
                </div>



                <!-- 查找替换面板 -->
                <div class="function-panel" id="findReplacePanel">
                    <div class="function-title">🔍 查找替换</div>
                    <input type="text" class="function-input" id="findText" placeholder="查找内容">
                    <input type="text" class="function-input" id="replaceText" placeholder="替换为">
                    <div style="margin-bottom: 6px;">
                        <label style="font-size: 11px;">
                            <input type="checkbox" id="caseSensitive"> 区分大小写
                        </label>
                        <label style="font-size: 11px; margin-left: 10px;">
                            <input type="checkbox" id="useRegex"> 正则表达式
                        </label>
                    </div>
                    <button class="btn" style="width: 48%; margin-right: 4%; font-size: 11px;" onclick="findNext()">查找下一个</button>
                    <button class="btn success" style="width: 48%; font-size: 11px;" onclick="replaceAll()">全部替换</button>
                    <div id="findResult" style="font-size: 10px; color: #666; margin-top: 4px;"></div>
                </div>

                <!-- 拆分文本面板 -->
                <div class="function-panel" id="splitTextPanel">
                    <div class="function-title">✂️ 拆分文本</div>
                    <input type="number" class="function-input" id="splitPages" placeholder="分成几页" min="2" value="2">
                    <button class="btn" style="width: 100%; margin-bottom: 6px; font-size: 11px;" onclick="splitText()">执行拆分</button>
                    <div class="split-result" id="splitResult"></div>
                    <div id="splitCopyButtons" style="display: flex; gap: 4px; margin-top: 6px; flex-wrap: wrap;">
                        <!-- 复制按钮将动态生成 -->
                    </div>
                    <button class="btn success" style="width: 100%; font-size: 11px; margin-top: 4px;" onclick="exportSplitResults()">导出拆分结果</button>
                </div>

                <!-- 快速检查面板 -->
                <div class="function-panel" id="quickCheckPanel">
                    <div class="function-title">⚡ 快速检查</div>
                    <div class="quick-check-result" id="quickCheckResult">
                        点击"快速检查"按钮开始检查...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-header">
                处理结果
                <span id="processedTextCount" style="float: right; font-size: 11px; color: #666; font-weight: normal;">0 字符</span>
            </div>
            <div class="panel-content">
                <div id="processedResult" class="text-output" contenteditable="true" style="outline: none;"
                     oninput="updateCharacterCount()"
                     onpaste="setTimeout(updateCharacterCount, 10)"
                     onclick="clearPreviousSelection()"
                     onfocus="clearPreviousSelection()">处理结果将在这里显示...</div>
            </div>
        </div>
    </div>
    


    <script>
        let autoProcessTimer = null;

        // 敏感词处理器类（简化版）
        class SensitiveWordProcessor {
            constructor() {
                this.sensitiveWords = new Map();
                this.categories = new Map();
                this.storageKey = 'sensitiveWords_userCustom';

                // 总是尝试从本地存储加载用户自定义敏感词
                // 不再有默认词库概念，所有敏感词都是用户自定义的
                const loaded = this.loadFromStorage();

                // 如果是第一次使用（没有存储的敏感词），添加一些示例
                if (!loaded || this.sensitiveWords.size === 0) {
                    this.initializeExampleWords();
                }
            }

            initializeDefaultWords() {
                // 默认词库已清除，保持空白状态
                // 用户的自定义敏感词将从本地存储加载
                console.log('📝 默认词库已清除，将从本地存储加载用户自定义敏感词');
            }

            initializeExampleWords() {
                // 添加一些示例敏感词，让用户了解功能
                const exampleWords = {
                    '白痴': '白吃',
                    '疯子': '风子',
                    '弱智': '若智'
                };

                for (const [word, replacement] of Object.entries(exampleWords)) {
                    this.sensitiveWords.set(word, {
                        replacement: replacement,
                        category: '示例词汇',
                        severity: 'medium',
                        enabled: true
                    });
                }

                console.log('📝 已添加示例敏感词，用户可以自行修改或删除');

                // 保存到本地存储
                this.saveToStorage();
            }

            registerCategory(categoryName, words, severity = 'medium') {
                this.categories.set(categoryName, {
                    words: words,
                    severity: severity,
                    enabled: true
                });

                for (const [word, replacement] of Object.entries(words)) {
                    this.sensitiveWords.set(word, {
                        replacement: replacement,
                        category: categoryName,
                        severity: severity
                    });
                }
            }

            addCustomWord(word, replacement, category = 'custom', severity = 'medium') {
                this.sensitiveWords.set(word, {
                    replacement: replacement,
                    category: category,
                    severity: severity
                });
                // 自动保存到本地存储
                this.saveToStorage();
            }

            removeWord(word) {
                const result = this.sensitiveWords.delete(word);
                if (result) {
                    // 自动保存到本地存储
                    this.saveToStorage();
                }
                return result;
            }

            processText(text, highlightOnly = false) {
                let processedText = text;
                const detectedWords = [];

                for (const [word, info] of this.sensitiveWords) {
                    const category = this.categories.get(info.category);
                    if (category && !category.enabled) continue;

                    const regex = new RegExp(this.escapeRegExp(word), 'gi');
                    const matches = processedText.match(regex);

                    if (matches) {
                        matches.forEach(match => {
                            detectedWords.push({
                                word: match,
                                replacement: info.replacement,
                                category: info.category,
                                severity: info.severity
                            });
                        });

                        if (highlightOnly) {
                            processedText = processedText.replace(regex, (match) =>
                                `<span class="highlight-symbol">${match}</span>`);
                        } else {
                            // 如果替换词是特殊标记，则只高亮不替换
                            if (info.replacement === '__HIGHLIGHT_ONLY__') {
                                processedText = processedText.replace(regex, (match) =>
                                    `<span class="highlight-symbol">${match}</span>`);
                            } else {
                                // 替换并高亮替换后的词，便于检查替换效果
                                processedText = processedText.replace(regex,
                                    `<span class="highlight-symbol">${info.replacement}</span>`);
                            }
                        }
                    }
                }

                return {
                    processedText: processedText,
                    detectedWords: detectedWords,
                    originalText: text
                };
            }

            getStatistics(text) {
                const result = this.processText(text, true);
                const stats = {
                    detectedCount: result.detectedWords.length,
                    severityCount: { high: 0, medium: 0, low: 0 }
                };

                result.detectedWords.forEach(item => {
                    stats.severityCount[item.severity]++;
                });

                return stats;
            }

            escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }

            // 添加敏感词
            addWord(word, category = 'custom', severity = 'medium') {
                this.sensitiveWords.set(word, {
                    replacement: word.split('').join('*'),
                    category: category,
                    severity: severity
                });
                // 自动保存到本地存储
                this.saveToStorage();
            }

            // 获取所有敏感词
            getAllWords() {
                const words = [];
                for (const [word, info] of this.sensitiveWords) {
                    words.push({
                        word: word,
                        category: info.category,
                        severity: info.severity,
                        replacement: info.replacement
                    });
                }
                return words;
            }

            // 保存到本地存储
            saveToStorage() {
                try {
                    const data = {
                        sensitiveWords: Array.from(this.sensitiveWords.entries()),
                        categories: Array.from(this.categories.entries()),
                        timestamp: Date.now()
                    };
                    localStorage.setItem(this.storageKey, JSON.stringify(data));
                    console.log('✅ 敏感词数据已保存到本地存储');
                    return true;
                } catch (error) {
                    console.error('❌ 保存敏感词数据失败:', error);
                    return false;
                }
            }

            // 从本地存储加载用户自定义敏感词
            loadFromStorage() {
                try {
                    const stored = localStorage.getItem(this.storageKey);
                    if (!stored) {
                        console.log('📝 本地存储为空，从空白状态开始');
                        return false; // 返回false表示没有加载到数据
                    }

                    const data = JSON.parse(stored);
                    if (data.sensitiveWords && data.categories) {
                        this.sensitiveWords = new Map(data.sensitiveWords);
                        this.categories = new Map(data.categories);
                        console.log('✅ 已从本地存储加载用户自定义敏感词');
                        console.log(`📊 加载了 ${this.sensitiveWords.size} 个敏感词`);
                        return true; // 成功加载数据
                    }
                    return false; // 数据格式不对，没有有效数据
                } catch (error) {
                    console.error('❌ 加载敏感词数据失败:', error);
                    return false; // 出错时返回false
                }
            }

            // 清除本地存储
            clearStorage() {
                try {
                    localStorage.removeItem(this.storageKey);
                    console.log('🗑️ 已清除本地存储的敏感词数据');
                    return true;
                } catch (error) {
                    console.error('❌ 清除敏感词数据失败:', error);
                    return false;
                }
            }
        }

        // 初始化处理器（会自动从本地存储加载或使用默认词库）
        let sensitiveProcessor = new SensitiveWordProcessor();

        // 字符计数更新函数
        function updateCharacterCount() {
            const originalText = document.getElementById('originalText');
            const processedResult = document.getElementById('processedResult');
            const originalCount = document.getElementById('originalTextCount');
            const processedCount = document.getElementById('processedTextCount');

            // 更新原始文本字符数
            const originalLength = originalText.value.length;
            originalCount.textContent = `${originalLength} 字符`;

            // 更新处理结果字符数（去除HTML标签）
            const processedContent = processedResult.textContent || processedResult.innerText || '';
            const processedLength = processedContent === '处理结果将在这里显示...' ? 0 : processedContent.length;
            processedCount.textContent = `${processedLength} 字符`;
        }

        // 快速检查 - 优化版本（防抖+缓存）
        let quickCheckTimeout;
        let lastQuickCheckText = '';
        let quickCheckCache = null;

        // 缓存变量
        let lastCheckText = '';
        let lastCheckResults = null;
        let lastSensitiveWordsHash = '';
        let checkTimeout = null;

        function performSensitiveCheck() {
            const resultsDiv = document.getElementById('quickCheckResults');
            const processedDiv = document.getElementById('processedResult');

            if (!processedDiv) {
                resultsDiv.innerHTML = '<div style="color: #dc3545; text-align: center; padding: 20px;">❌ 未找到处理结果区域</div>';
                return;
            }

            // 显示加载状态
            resultsDiv.innerHTML = `
                <div style="text-align: center; color: #6c757d; padding: 20px;">
                    <div style="font-size: 20px; margin-bottom: 6px; opacity: 0.6;">🔄</div>
                    <div style="font-size: 12px;">正在检查敏感词...</div>
                </div>
            `;

            // 进一步优化渲染时机和速度
            if (checkTimeout) clearTimeout(checkTimeout);

            // 立即开始处理，不等待动画帧
            checkTimeout = setTimeout(() => {
                performSensitiveCheckAsync(resultsDiv, processedDiv);
            }, 5); // 进一步减少延迟时间
        }

        function performSensitiveCheckAsync(resultsDiv, processedDiv) {
            // 获取文本
            let text = processedDiv.textContent || processedDiv.innerText || '';
            if (!text && processedDiv.innerHTML) {
                text = processedDiv.innerHTML.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');
            }
            text = text.trim();

            if (!text) {
                resultsDiv.innerHTML = '<div style="color: #666; text-align: center; padding: 20px;">📝 请先输入文本内容</div>';
                return;
            }

            // 从敏感词管理器获取所有敏感词
            const allSensitiveWords = sensitiveProcessor.getAllWords();

            // 生成敏感词列表的哈希值用于缓存比较
            const currentSensitiveWordsHash = JSON.stringify(allSensitiveWords.map(w => w.word).sort());

            // 检查缓存（文本内容和敏感词列表都要匹配）
            if (text === lastCheckText && currentSensitiveWordsHash === lastSensitiveWordsHash && lastCheckResults) {
                displayResults(resultsDiv, lastCheckResults);
                return;
            }
            const foundWords = new Set(); // 使用 Set 避免重复

            // 如果没有配置敏感词，显示提示
            if (allSensitiveWords.length === 0) {
                resultsDiv.innerHTML = `
                    <div style="color: #666; text-align: center; padding: 20px;">
                        <div style="font-size: 20px; margin-bottom: 10px;">🛡️</div>
                        <div style="font-size: 14px; margin-bottom: 8px;">暂无敏感词配置</div>
                        <div style="font-size: 12px; color: #999;">请先在"敏感词管理"中添加敏感词</div>
                    </div>
                `;
                return;
            }

            // 构建敏感词映射缓存用于检测
            const wordMap = buildSensitiveWordMap(allSensitiveWords);

            // 批量检查敏感词管理器中的所有敏感词（包括替换后的词）
            for (const wordInfo of allSensitiveWords) {
                const originalWord = wordInfo.word;

                // 检查原始敏感词
                if (text.indexOf(originalWord) !== -1) {
                    foundWords.add(originalWord);
                    continue;
                }

                // 检查替换后的词
                const replacement = wordInfo.replacement;
                if (replacement && replacement !== '__HIGHLIGHT_ONLY__') {
                    if (text.indexOf(replacement) !== -1) {
                        foundWords.add(originalWord); // 记录原始敏感词，便于后续处理
                    }
                }
            }

            // 如果找到敏感词，直接从处理结果中提取包含敏感词的句子
            if (foundWords.size > 0) {
                // 获取处理结果的DOM元素
                const processedResultElement = document.getElementById('processedResult');
                if (!processedResultElement) {
                    console.error('❌ 未找到处理结果元素');
                    return;
                }

                // 获取处理结果的HTML内容，然后转换为按行分割的文本
                const processedHTML = processedResultElement.innerHTML || '';

                if (!processedHTML || processedHTML.includes('处理结果将在这里显示')) {
                    console.log('⚠️ 处理结果为空，无法进行快速检查');
                    return;
                }

                // 将HTML中的<br>标签替换为换行符，然后分割
                let processedText = processedHTML
                    .replace(/<br\s*\/?>/gi, '\n')  // 将<br>标签替换为换行符
                    .replace(/<[^>]*>/g, '')        // 移除其他HTML标签
                    .replace(/&nbsp;/g, ' ');       // 替换HTML空格

                // 按行分割处理结果
                const processedLines = processedText.split('\n').filter(line => line.trim().length > 0);

                console.log('🔍 从处理结果中提取的行数:', processedLines.length);
                console.log('🔍 处理结果示例（前10行）:');
                processedLines.slice(0, 10).forEach((line, i) => {
                    console.log(`  ${i + 1}. "${line.trim()}" (长度: ${line.trim().length})`);
                });

                const results = [];

                console.log('🔍 要查找的敏感词:', Array.from(foundWords));

                // 遍历处理结果的每一行，摘录所有包含敏感词的句子
                for (let i = 0; i < processedLines.length; i++) {
                    const line = processedLines[i].trim();

                    if (!line) continue;

                    // 检查这一行是否包含任何敏感词（原始词或替换后的词）
                    let hasKeyword = false;
                    let foundKeyword = '';

                    for (const originalWord of foundWords) {
                        // 先检查原始敏感词
                        if (line.includes(originalWord)) {
                            hasKeyword = true;
                            foundKeyword = originalWord;
                            break;
                        }

                        // 再检查替换后的词
                        const wordInfo = wordMap.get(originalWord);
                        if (wordInfo && wordInfo.replacement && wordInfo.replacement !== '__HIGHLIGHT_ONLY__') {
                            if (line.includes(wordInfo.replacement)) {
                                hasKeyword = true;
                                foundKeyword = originalWord; // 仍然记录原始敏感词
                                break;
                            }
                        }
                    }

                    // 如果这一行包含敏感词，就添加到结果中
                    if (hasKeyword) {
                        results.push({
                            sentence: line,
                            word: foundKeyword,
                            index: results.length + 1
                        });

                        console.log(`✅ 从处理结果中摘录包含"${foundKeyword}"的完整句子: "${line}" (长度: ${line.length})`);
                    }

                    // 限制结果数量，避免过多结果导致卡顿
                    if (results.length >= 50) break;
                }

                // 缓存结果
                lastCheckText = text;
                lastCheckResults = results;
                lastSensitiveWordsHash = currentSensitiveWordsHash;

                displayResults(resultsDiv, results);
            } else {
                const results = [];
                lastCheckText = text;
                lastCheckResults = results;
                resultsDiv.innerHTML = '<div style="color: #28a745; text-align: center; padding: 20px;">✅ 未发现敏感词</div>';

                // 同时更新面板显示
                const panelDiv = document.getElementById('quickCheckResult');
                if (panelDiv) {
                    panelDiv.innerHTML = '<div style="color: #28a745; text-align: center; padding: 15px; font-size: 12px;">✅ 未发现敏感词</div>';
                }
            }
        }





        // 优化的显示逻辑，减少弹窗卡顿
        function displayResults(resultsDiv, results) {
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div style="color: #dc3545; text-align: center; padding: 20px;">❌ 提取句子失败</div>';

                // 同时更新面板显示
                const panelDiv = document.getElementById('quickCheckResult');
                if (panelDiv) {
                    panelDiv.innerHTML = '<div style="color: #dc3545; text-align: center; padding: 15px; font-size: 12px;">❌ 提取句子失败</div>';
                }
                return;
            }

            // 立即显示容器，避免空白等待
            resultsDiv.innerHTML = '<div style="padding: 10px;" id="quickCheckContainer"></div>';
            const container = document.getElementById('quickCheckContainer');

            // 构建敏感词映射缓存（一次性预处理）
            const allSensitiveWords = sensitiveProcessor.getAllWords();
            const wordMap = buildSensitiveWordMap(allSensitiveWords);

            // 分批渲染，避免一次性处理大量DOM导致卡顿
            renderResultsBatch(container, results, 0, wordMap);



        }

        // 优化的分批渲染函数，使用预处理缓存
        function renderResultsBatch(container, results, startIndex, wordMap) {
            const batchSize = 8; // 增加批处理大小，减少分批次数
            const endIndex = Math.min(startIndex + batchSize, results.length);

            // 使用DocumentFragment优化DOM操作
            const fragment = document.createDocumentFragment();

            for (let i = startIndex; i < endIndex; i++) {
                const result = results[i];
                const item = document.createElement('div');
                item.className = 'quick-check-item';
                item.setAttribute('data-sentence', result.sentence);
                item.setAttribute('data-word', result.word);
                item.setAttribute('data-index', i);
                item.style.cssText = 'margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; cursor: pointer; font-size: 13px; line-height: 1.4; transition: all 0.2s ease;';

                // 使用预处理缓存进行智能高亮
                const highlightedSentence = getSmartHighlightedSentence(result.sentence, result.word, wordMap);

                item.innerHTML = `
                    <span style="color: #007bff; font-weight: bold; margin-right: 5px;">${result.index}.</span>
                    ${highlightedSentence}
                `;

                // 进一步优化事件绑定 - 使用事件委托减少内存占用
                item.addEventListener('click', () => quickCheckItemClick(item, i));

                // 使用CSS hover效果替代JavaScript事件，提升性能
                item.onmouseenter = () => {
                    item.style.backgroundColor = '#e9ecef';
                    item.style.borderColor = '#007bff';
                };
                item.onmouseleave = () => {
                    item.style.backgroundColor = '#f9f9f9';
                    item.style.borderColor = '#ddd';
                };

                fragment.appendChild(item);
            }

            container.appendChild(fragment);

            // 如果还有更多项目，继续分批处理
            if (endIndex < results.length) {
                requestAnimationFrame(() => {
                    renderResultsBatch(container, results, endIndex, wordMap);
                });
            } else {
                // 所有项目渲染完成，更新面板显示
                updatePanelDisplay(results, wordMap);
            }
        }

        // 智能高亮函数（使用缓存，避免重复查找）
        function getSmartHighlightedSentence(sentence, originalWord, wordMap) {
            const wordInfo = wordMap.get(originalWord);

            if (wordInfo && wordInfo.replacement && wordInfo.replacement !== '__HIGHLIGHT_ONLY__') {
                // 有替换词，尝试高亮替换词
                const replacementWord = wordInfo.replacement;
                if (sentence.includes(replacementWord)) {
                    // 高亮替换词
                    return sentence.replace(
                        new RegExp(escapeRegExp(replacementWord), 'g'),
                        `<span style="background: #ffeb3b; padding: 1px 3px; border-radius: 2px;">${replacementWord}</span>`
                    );
                }
            }

            // 回退到高亮原始敏感词
            return sentence.replace(
                new RegExp(escapeRegExp(originalWord), 'g'),
                `<span style="background: #ffeb3b; padding: 1px 3px; border-radius: 2px;">${originalWord}</span>`
            );
        }

        // 更新面板显示的函数
        function updatePanelDisplay(results, wordMap) {
            const panelDiv = document.getElementById('quickCheckResult');
            if (panelDiv) {
                // 为面板创建简化版本的显示内容
                let panelHtml = '<div style="padding: 8px; font-size: 12px;">';

                results.forEach((result, index) => {
                    // 使用相同的智能高亮逻辑
                    const highlightedSentence = getSmartHighlightedSentence(result.sentence, result.word, wordMap);

                    panelHtml += `
                        <div style="margin-bottom: 6px; padding: 6px; border: 1px solid #ddd; border-radius: 3px; background: #f9f9f9; font-size: 11px; line-height: 1.3;">
                            <span style="color: #007bff; font-weight: bold; margin-right: 3px;">${result.index}.</span>
                            ${highlightedSentence}
                        </div>
                    `;
                });

                panelHtml += '</div>';
                panelDiv.innerHTML = panelHtml;
            }
        }

        // 优化的快速检查项目点击处理
        let lastSelectedItem = null;

        function quickCheckItemClick(element, index) {
            try {
                // 快速获取数据
                const sentence = element.getAttribute('data-sentence');
                const word = element.getAttribute('data-word');

                // 优化的选中状态更新：只更新必要的元素
                if (lastSelectedItem && lastSelectedItem !== element) {
                    lastSelectedItem.style.backgroundColor = '#f9f9f9';
                    lastSelectedItem.style.borderColor = '#ddd';
                }

                element.style.backgroundColor = '#e3f2fd';
                element.style.borderColor = '#2196f3';
                lastSelectedItem = element;

                // 立即开始定位，不等待样式更新完成
                requestAnimationFrame(() => {
                    locateTextInResult(word, sentence);
                });

            } catch (error) {
                console.error('❌ 点击处理出错:', error);
            }
        }

        // 优化的快速定位缓存
        let locationCache = new Map();
        let lastProcessedHTML = '';

        // 敏感词映射缓存（性能优化）
        let sensitiveWordMap = new Map();
        let lastSensitiveWordsMapHash = '';

        // 清除定位缓存（当文本处理结果更新时调用）
        function clearLocationCache() {
            locationCache.clear();
            lastProcessedHTML = '';
        }

        // 构建敏感词映射缓存
        function buildSensitiveWordMap(allSensitiveWords) {
            const currentHash = JSON.stringify(allSensitiveWords.map(w => w.word + w.replacement).sort());

            // 如果敏感词没有变化，使用缓存
            if (currentHash === lastSensitiveWordsMapHash && sensitiveWordMap.size > 0) {
                return sensitiveWordMap;
            }

            // 重新构建映射
            sensitiveWordMap.clear();
            allSensitiveWords.forEach(wordInfo => {
                sensitiveWordMap.set(wordInfo.word, {
                    replacement: wordInfo.replacement,
                    category: wordInfo.category,
                    severity: wordInfo.severity
                });
            });

            lastSensitiveWordsMapHash = currentHash;
            return sensitiveWordMap;
        }

        // 在处理结果中定位特定句子（优化版）
        function locateTextInResult(word, sentence) {
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv) {
                return;
            }

            // 立即清除之前的定位高亮，减少视觉延迟
            clearPreviousLocationHighlight();

            const processedHTML = processedDiv.innerHTML || '';
            const cleanSentence = sentence.trim();

            // 使用缓存机制加速文本处理
            let processedLines;
            if (processedHTML === lastProcessedHTML && locationCache.has('processedLines')) {
                processedLines = locationCache.get('processedLines');
            } else {
                // 优化的文本处理：减少正则表达式调用
                let processedText = processedHTML
                    .replace(/<br\s*\/?>/gi, '\n')
                    .replace(/<[^>]*>/g, '')
                    .replace(/&nbsp;/g, ' ');

                processedLines = processedText.split('\n').filter(line => line.trim().length > 0);

                // 缓存结果
                locationCache.set('processedLines', processedLines);
                lastProcessedHTML = processedHTML;
            }

            // 快速查找目标行
            let targetLine = '';
            for (let i = 0; i < processedLines.length; i++) {
                if (processedLines[i].trim().includes(cleanSentence)) {
                    targetLine = processedLines[i].trim();
                    break;
                }
            }

            if (!targetLine) {
                console.log('❌ 未找到包含目标句子的行');
                return;
            }

            console.log('🎯 找到目标行:', targetLine);

            // 获取处理结果的纯文本内容用于定位
            const fullText = processedDiv.textContent || processedDiv.innerText || '';

            // 查找目标行在文本中的位置
            let targetIndex = fullText.indexOf(targetLine);

            if (targetIndex !== -1) {
                // 使用Range API精确定位
                const range = document.createRange();
                const walker = document.createTreeWalker(
                    processedDiv,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let currentPos = 0;
                let textNode;
                let startNode = null;
                let startOffset = 0;
                let endNode = null;
                let endOffset = 0;

                // 找到包含目标行开始和结束位置的文本节点
                while (textNode = walker.nextNode()) {
                    const nodeLength = textNode.textContent.length;

                    // 找到开始节点
                    if (!startNode && currentPos + nodeLength > targetIndex) {
                        startNode = textNode;
                        startOffset = targetIndex - currentPos;
                    }

                    // 找到结束节点
                    if (!endNode && currentPos + nodeLength >= targetIndex + targetLine.length) {
                        endNode = textNode;
                        endOffset = targetIndex + targetLine.length - currentPos;
                        break;
                    }

                    currentPos += nodeLength;
                }

                if (startNode && endNode) {
                    try {
                        // 设置range覆盖完整行
                        range.setStart(startNode, Math.max(0, startOffset));
                        range.setEnd(endNode, Math.min(endNode.textContent.length, endOffset));

                        // 创建临时高亮元素用于定位
                        const tempSpan = document.createElement('span');
                        tempSpan.className = 'location-highlight';
                        tempSpan.style.cssText = 'background: rgba(255, 152, 0, 0.3); padding: 4px; border-radius: 4px; border-left: 3px solid #ff9800; display: inline-block;';

                        // 插入高亮元素
                        try {
                            range.surroundContents(tempSpan);
                        } catch (e) {
                            // 如果surroundContents失败，使用备用方案
                            const contents = range.extractContents();
                            tempSpan.appendChild(contents);
                            range.insertNode(tempSpan);
                        }

                        // 优化的快速滚动
                        requestAnimationFrame(() => {
                            tempSpan.scrollIntoView({
                                behavior: 'instant',  // 使用instant获得最快速度
                                block: 'center',
                                inline: 'nearest'
                            });

                            // 添加短暂的视觉反馈动画
                            tempSpan.style.transition = 'all 0.15s ease-out';
                            tempSpan.style.transform = 'scale(1.02)';
                            setTimeout(() => {
                                tempSpan.style.transform = 'scale(1)';
                            }, 150);
                        });

                        console.log('✅ 已定位到完整行');
                        return;
                    } catch (error) {
                        console.log('Range定位失败，使用备用方案:', error);
                    }
                }
            }

            // 备用方案：基于HTML的完整行高亮
            console.log('使用备用定位方案');
            const currentHTML = processedDiv.innerHTML;

            // 如果已经找到了目标行，直接在HTML中高亮
            if (targetLine) {
                const escapedTargetLine = escapeRegExp(targetLine);
                const highlightedHTML = currentHTML.replace(
                    new RegExp(`(${escapedTargetLine})`, 'gi'),
                    '<span class="location-highlight" style="background: rgba(255, 152, 0, 0.3); padding: 4px; border-radius: 4px; border-left: 3px solid #ff9800; display: inline-block;">$1</span>'
                );

                if (highlightedHTML !== currentHTML) {
                    processedDiv.innerHTML = highlightedHTML;

                    // 优化的备用方案滚动
                    const locationHighlight = processedDiv.querySelector('.location-highlight');
                    if (locationHighlight) {
                        requestAnimationFrame(() => {
                            locationHighlight.scrollIntoView({
                                behavior: 'instant',
                                block: 'center',
                                inline: 'nearest'
                            });

                            // 快速视觉反馈
                            locationHighlight.style.transition = 'all 0.15s ease-out';
                            locationHighlight.style.transform = 'scale(1.02)';
                            setTimeout(() => {
                                locationHighlight.style.transform = 'scale(1)';
                            }, 150);
                        });
                    }
                    return;
                }
            }

            // 最后的备用方案：只高亮原句子
            const escapedSentence = escapeRegExp(cleanSentence);
            const highlightedHTML = currentHTML.replace(
                new RegExp(`(${escapedSentence})`, 'gi'),
                '<span class="location-highlight" style="background: rgba(255, 152, 0, 0.3); padding: 4px; border-radius: 4px; border-left: 3px solid #ff9800; display: inline-block;">$1</span>'
            );

            if (highlightedHTML !== currentHTML) {
                processedDiv.innerHTML = highlightedHTML;

                // 最终备用方案的快速滚动
                const locationHighlight = processedDiv.querySelector('.location-highlight');
                if (locationHighlight) {
                    requestAnimationFrame(() => {
                        locationHighlight.scrollIntoView({
                            behavior: 'instant',
                            block: 'center',
                            inline: 'nearest'
                        });

                        // 快速视觉反馈
                        locationHighlight.style.transition = 'all 0.15s ease-out';
                        locationHighlight.style.transform = 'scale(1.02)';
                        setTimeout(() => {
                            locationHighlight.style.transform = 'scale(1)';
                        }, 150);
                    });
                }
            }
        }

        // 清除之前的高亮
        function clearPreviousHighlight() {
            const processedDiv = document.getElementById('processedResult');
            if (processedDiv) {
                // 清除敏感词高亮
                const highlights = processedDiv.querySelectorAll('.temp-highlight');
                highlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });

                // 清除句子高亮
                const sentenceHighlights = processedDiv.querySelectorAll('.sentence-highlight');
                sentenceHighlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });
            }
        }

        // 只清除定位高亮，保留原有的敏感词高亮
        function clearPreviousLocationHighlight() {
            const processedDiv = document.getElementById('processedResult');
            if (processedDiv) {
                // 清除定位高亮元素，但保留内部的敏感词高亮结构
                const locationHighlights = processedDiv.querySelectorAll('.location-highlight');
                locationHighlights.forEach(element => {
                    const parent = element.parentNode;
                    if (parent) {
                        // 保留元素内的HTML内容（包括敏感词高亮），而不是纯文本
                        const innerHTML = element.innerHTML;

                        // 创建临时容器来解析HTML
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = innerHTML;

                        // 将临时容器的所有子节点移动到父元素中
                        while (tempDiv.firstChild) {
                            parent.insertBefore(tempDiv.firstChild, element);
                        }

                        // 移除location-highlight元素
                        parent.removeChild(element);

                        // 合并相邻的文本节点
                        parent.normalize();
                    }
                });
            }
        }

        // 显示敏感词检查结果
        function displaySensitiveResults(results) {
            const resultsDiv = document.getElementById('quickCheckResults');
            const panelDiv = document.getElementById('quickCheckResult'); // 面板中的显示区域

            let html = `
                <div style="padding: 10px;">
            `;

            results.forEach((result, index) => {
                const highlightedSentence = result.sentence.replace(
                    result.word,
                    `<span style="background: #ffeb3b; padding: 1px 3px; border-radius: 2px;">${result.word}</span>`
                );

                html += `
                    <div class="quick-check-item"
                         data-sentence="${result.sentence.replace(/"/g, '&quot;')}"
                         data-index="${index}"
                         style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; cursor: pointer; font-size: 13px; line-height: 1.4;"
                         onclick="directQuickCheckClick(this, ${index})"
                         onmouseover="this.style.backgroundColor='#e9ecef'"
                         onmouseout="this.style.backgroundColor='#f9f9f9'">
                        <span style="color: #007bff; font-weight: bold; margin-right: 5px;">${result.index}.</span>
                        ${highlightedSentence}
                    </div>
                `;
            });

            html += '</div>';
            resultsDiv.innerHTML = html;

            // 同时更新面板中的显示区域
            if (panelDiv) {
                // 为面板创建简化版本的显示内容
                let panelHtml = `
                    <div style="padding: 8px; font-size: 12px;">
                `;

                results.forEach((result, index) => {
                    const highlightedSentence = result.sentence.replace(
                        result.word,
                        `<span style="background: #ffeb3b; padding: 1px 2px; border-radius: 2px;">${result.word}</span>`
                    );

                    panelHtml += `
                        <div style="margin-bottom: 6px; padding: 6px; border: 1px solid #ddd; border-radius: 3px; background: #f9f9f9; font-size: 11px; line-height: 1.3;">
                            <span style="color: #007bff; font-weight: bold; margin-right: 3px;">${result.index}.</span>
                            ${highlightedSentence}
                        </div>
                    `;
                });

                panelHtml += '</div>';
                panelDiv.innerHTML = panelHtml;
            }

            console.log('✅ 敏感词检查结果显示完成');
        }
















        // 清除选中效果（不清除粉色阴影和敏感词高亮）
        function clearPreviousSelection_old1() {
            const processedDiv = document.getElementById('processedResult');
            if (processedDiv) {
                let html = processedDiv.innerHTML;
                html = html.replace(/<span class="sentence-highlight"[^>]*>(.*?)<\/span>/g, '$1');
                processedDiv.innerHTML = html;
            }

            // 注意：不再自动清除粉色阴影，只有点击处理结果文本框才清除
            console.log('📝 清除选中效果，保留粉色阴影');
        }

        // 创建临时高亮
        function createTemporaryHighlight(element, charIndex, length) {
            console.log('创建临时高亮，位置:', charIndex, '长度:', length);

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            let currentIndex = 0;

            while (textNode = walker.nextNode()) {
                const nodeText = textNode.textContent;
                const nodeEndIndex = currentIndex + nodeText.length;

                if (charIndex >= currentIndex && charIndex < nodeEndIndex) {
                    const relativeIndex = charIndex - currentIndex;
                    const endIndex = Math.min(relativeIndex + length, nodeText.length);

                    try {
                        const range = document.createRange();
                        range.setStart(textNode, relativeIndex);
                        range.setEnd(textNode, endIndex);

                        const span = document.createElement('span');
                        span.className = 'sentence-highlight';
                        span.style.cssText = 'background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 10px 12px; border-radius: 6px; font-weight: bold; box-shadow: 0 0 25px rgba(255, 182, 193, 0.9);';

                        range.surroundContents(span);

                        // 滚动到高亮位置
                        span.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // 添加整句粉色阴影效果
                        setTimeout(() => {
                            createWholeSentenceShadow(span);
                        }, 300);

                        console.log('临时高亮创建成功');
                        return;
                    } catch (error) {
                        console.log('创建临时高亮失败:', error);
                    }
                }

                currentIndex = nodeEndIndex;
            }

            console.log('未找到指定位置的文本');
        }

        // 提取关键词
        function extractKeywords(text) {
            const keywords = [];

            // 提取长度大于2的连续中文字符
            const chineseMatches = text.match(/[\u4e00-\u9fa5]{3,}/g);
            if (chineseMatches) {
                keywords.push(...chineseMatches);
            }

            // 提取英文单词
            const englishMatches = text.match(/[a-zA-Z]{3,}/g);
            if (englishMatches) {
                keywords.push(...englishMatches);
            }

            // 按长度排序，优先匹配长的关键词
            return keywords.sort((a, b) => b.length - a.length);
        }

        // 精确字符定位高亮
        function preciseCharacterHighlight(element, targetText, highlightStyle) {
            console.log('开始精确字符定位');

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            let fullText = '';
            const textNodes = [];

            // 收集所有文本节点
            while (textNode = walker.nextNode()) {
                if (textNode.textContent.trim()) {
                    textNodes.push({
                        node: textNode,
                        text: textNode.textContent,
                        startIndex: fullText.length
                    });
                    fullText += textNode.textContent;
                }
            }

            console.log('收集到文本节点数量:', textNodes.length);
            console.log('完整文本长度:', fullText.length);
            console.log('完整文本前100字符:', fullText.substring(0, 100));

            // 在完整文本中查找目标文本
            let targetIndex = fullText.indexOf(targetText);
            if (targetIndex === -1) {
                // 尝试清理空格后查找
                const cleanFullText = fullText.replace(/\s+/g, ' ');
                const cleanTargetText = targetText.replace(/\s+/g, ' ');
                targetIndex = cleanFullText.indexOf(cleanTargetText);
                if (targetIndex !== -1) {
                    console.log('清理空格后找到匹配位置:', targetIndex);
                } else {
                    console.log('在完整文本中未找到目标文本');
                    return false;
                }
            } else {
                console.log('在完整文本中找到目标文本位置:', targetIndex);
            }

            // 找到包含目标文本的节点范围
            const targetEndIndex = targetIndex + targetText.length;
            let startNode = null, endNode = null;
            let startOffset = 0, endOffset = 0;

            for (const nodeInfo of textNodes) {
                const nodeEndIndex = nodeInfo.startIndex + nodeInfo.text.length;

                if (!startNode && nodeEndIndex > targetIndex) {
                    startNode = nodeInfo.node;
                    startOffset = targetIndex - nodeInfo.startIndex;
                }

                if (nodeEndIndex >= targetEndIndex) {
                    endNode = nodeInfo.node;
                    endOffset = targetEndIndex - nodeInfo.startIndex;
                    break;
                }
            }

            if (startNode && endNode) {
                console.log('找到目标节点范围');
                try {
                    const range = document.createRange();
                    range.setStart(startNode, Math.max(0, startOffset));
                    range.setEnd(endNode, Math.min(endNode.textContent.length, endOffset));

                    const span = document.createElement('span');
                    span.className = 'sentence-highlight';
                    span.style.cssText = highlightStyle;

                    range.surroundContents(span);
                    console.log('成功创建高亮范围');
                    return true;
                } catch (e) {
                    console.log('创建范围失败:', e.message);
                    return false;
                }
            }

            return false;
        }

        // DOM节点遍历高亮
        function highlightByDOMTraversal(element, targetText, highlightStyle) {
            console.log('开始DOM节点遍历高亮');

            const allElements = element.querySelectorAll('*');

            for (const el of allElements) {
                const textContent = el.textContent || '';
                if (textContent.includes(targetText)) {
                    console.log('在元素中找到目标文本:', el.tagName);

                    // 如果是叶子节点，直接处理
                    if (el.children.length === 0) {
                        const innerHTML = el.innerHTML;
                        if (innerHTML.includes(targetText)) {
                            const escapedText = targetText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                            el.innerHTML = innerHTML.replace(
                                new RegExp(escapedText, 'g'),
                                `<span class="sentence-highlight" style="${highlightStyle}">${targetText}</span>`
                            );
                            console.log('叶子节点高亮成功');
                            return true;
                        }
                    }
                }
            }

            console.log('DOM节点遍历未找到匹配');
            return false;
        }

        // 创建临时高亮
        function createTemporaryHighlight(element, charIndex, length) {
            console.log('创建临时高亮，位置:', charIndex, '长度:', length);

            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            let currentIndex = 0;

            while (textNode = walker.nextNode()) {
                const nodeText = textNode.textContent;
                const nodeEndIndex = currentIndex + nodeText.length;

                if (charIndex >= currentIndex && charIndex < nodeEndIndex) {
                    const relativeIndex = charIndex - currentIndex;
                    const endIndex = Math.min(relativeIndex + length, nodeText.length);

                    try {
                        const range = document.createRange();
                        range.setStart(textNode, relativeIndex);
                        range.setEnd(textNode, endIndex);

                        const span = document.createElement('span');
                        span.className = 'sentence-highlight';
                        span.style.cssText = 'background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 10px 12px; border-radius: 6px; font-weight: bold; box-shadow: 0 0 25px rgba(255, 182, 193, 0.9);';

                        range.surroundContents(span);

                        // 滚动到高亮位置
                        span.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // 添加整句粉色阴影效果
                        setTimeout(() => {
                            createWholeSentenceShadow(span);
                        }, 300);

                        console.log('临时高亮创建成功');
                        return;
                    } catch (e) {
                        console.log('临时高亮创建失败:', e.message);
                    }
                }

                currentIndex = nodeEndIndex;
            }

            console.log('无法创建临时高亮');
        }

        // 忽略HTML标签的文本高亮
        function highlightTextIgnoringTags(html, targetText, highlightStyle) {
            console.log('开始逐字符匹配，目标文本:', targetText);

            let result = '';
            let textIndex = 0;
            let i = 0;
            let inTag = false;

            while (i < html.length && textIndex < targetText.length) {
                const char = html[i];

                if (char === '<') {
                    inTag = true;
                    result += char;
                } else if (char === '>') {
                    inTag = false;
                    result += char;
                } else if (inTag) {
                    result += char;
                } else {
                    // 不在标签内，比较字符
                    if (char === targetText[textIndex]) {
                        if (textIndex === 0) {
                            // 开始匹配，插入高亮开始标签
                            result += `<span class="sentence-highlight" style="${highlightStyle}">`;
                        }
                        result += char;
                        textIndex++;

                        if (textIndex === targetText.length) {
                            // 匹配完成，插入高亮结束标签
                            result += '</span>';
                            // 添加剩余的HTML
                            result += html.substring(i + 1);
                            console.log('逐字符匹配成功');
                            return { success: true, html: result };
                        }
                    } else {
                        // 字符不匹配，重置
                        if (textIndex > 0) {
                            // 如果之前有部分匹配，需要回退
                            console.log('字符不匹配，重置匹配');
                            return { success: false, html: html };
                        }
                        result += char;
                    }
                }
                i++;
            }

            console.log('逐字符匹配失败');
            return { success: false, html: html };
        }

        // 滚动到高亮位置并添加整句粉色阴影效果
        function scrollToHighlight(element) {
            setTimeout(() => {
                const highlight = element.querySelector('.sentence-highlight');
                if (highlight) {
                    console.log('🎯 滚动到高亮位置，准备添加整句粉色阴影');
                    highlight.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 直接创建整句阴影覆盖
                    setTimeout(() => {
                        createWholeSentenceShadow(highlight);
                    }, 300);
                } else {
                    console.log('❌ 未找到高亮元素');
                }
            }, 100);
        }

        // 创建简单高效的粉色阴影覆盖（防抖优化）
        function createWholeSentenceShadow(highlightElement) {
            console.log('🌸 创建粉色阴影覆盖');

            // 防抖：如果已经有阴影在同一个元素上，直接返回
            if (window.hasPinkShadow && highlightElement.classList.contains('pink-shadow-active')) {
                console.log('⚡ 阴影已存在，跳过创建');
                return;
            }

            // 快速清除之前的阴影
            if (window.hasPinkShadow) {
                clearPinkShadowQuick();
            }

            // 快速选择阴影目标元素（性能优化）
            let targetElement = highlightElement;

            // 简化的容器选择 - 最多向上查找2层，减少计算
            let parent = highlightElement.parentElement;
            for (let i = 0; i < 2 && parent && parent.id !== 'processedResult'; i++) {
                const parentText = parent.textContent || '';

                // 简化条件：只检查文本长度和标签类型
                if (parentText.length < 120 &&
                    (parent.tagName === 'SPAN' || parent.tagName === 'EM' || parent.tagName === 'STRONG')) {
                    targetElement = parent;
                    break;
                }

                parent = parent.parentElement;
            }

            console.log('🎯 阴影目标:', targetElement.tagName);

            // 简化的显示类型处理 - 只处理明显的块级元素
            if (targetElement.tagName === 'DIV' || targetElement.tagName === 'P' || targetElement.tagName === 'LI') {
                targetElement.style.display = 'inline-block';
                targetElement.setAttribute('data-original-display', 'block');
            }

            // 直接添加阴影类
            targetElement.classList.add('pink-shadow-active');

            // 添加简化的阴影样式（保持行内显示，不分行）
            if (!document.getElementById('pink-shadow-style')) {
                const style = document.createElement('style');
                style.id = 'pink-shadow-style';
                style.textContent = `
                    .pink-shadow-active {
                        background: rgba(255, 182, 193, 0.85) !important;
                        color: rgba(139, 69, 19, 1) !important;
                        padding: 2px 6px !important;
                        border-radius: 3px !important;
                        display: inline !important;
                        margin: 0 !important;
                        border: none !important;
                        outline: none !important;
                        line-height: 1 !important;
                        vertical-align: baseline !important;
                        white-space: nowrap !important;
                        word-break: keep-all !important;
                        overflow: visible !important;
                        position: relative !important;
                        z-index: 1 !important;

                        /* 硬件加速优化 */
                        will-change: opacity !important;
                        transform: translateZ(0) !important;
                        backface-visibility: hidden !important;

                        /* 简化的脉动效果 - 只改变透明度 */
                        animation: pinkPulseSimple 3s ease-in-out infinite !important;
                    }

                    .pink-shadow-active * {
                        background: transparent !important;
                        color: inherit !important;
                        display: inline !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        border: none !important;
                        outline: none !important;
                        line-height: inherit !important;
                        vertical-align: baseline !important;
                        white-space: inherit !important;
                        word-break: inherit !important;
                    }

                    /* 优化的动画 - 只改变透明度，避免重绘 */
                    @keyframes pinkPulseSimple {
                        0%, 100% {
                            opacity: 0.85;
                        }
                        50% {
                            opacity: 1;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // 设置状态
            window.hasPinkShadow = true;
            console.log('✅ 粉色阴影已添加');
        }



        // 为整句添加粉色阴影
        function addWholeSentenceShadow(sentenceElement) {
            console.log('🌸 为整句添加粉色阴影覆盖');

            // 移除之前的阴影
            removeSentenceShadow();

            // 添加脉动动画类
            sentenceElement.classList.add('sentence-shadow-pulse');

            // 添加脉动动画样式
            if (!document.getElementById('pulse-animation-style')) {
                const pulseStyle = document.createElement('style');
                pulseStyle.id = 'pulse-animation-style';
                pulseStyle.textContent = `
                    .sentence-shadow-pulse {
                        background: rgba(255, 182, 193, 0.8) !important;
                        color: rgba(139, 69, 19, 1) !important;
                        padding: 12px 15px !important;
                        border-radius: 8px !important;
                        box-shadow: 0 0 25px rgba(255, 182, 193, 0.9) !important;
                        animation: sentencePulse 2.5s ease-in-out infinite !important;
                        display: inline-block !important;
                        line-height: 1.6 !important;
                        margin: 8px 0 !important;
                    }

                    .sentence-shadow-pulse * {
                        background: transparent !important;
                        color: inherit !important;
                    }

                    @keyframes sentencePulse {
                        0% {
                            background: rgba(255, 182, 193, 0.8) !important;
                            box-shadow: 0 0 25px rgba(255, 182, 193, 0.9) !important;
                            transform: scale(1);
                        }
                        50% {
                            background: rgba(255, 182, 193, 0.95) !important;
                            box-shadow: 0 0 35px rgba(255, 182, 193, 1) !important;
                            transform: scale(1.02);
                        }
                        100% {
                            background: rgba(255, 182, 193, 0.8) !important;
                            box-shadow: 0 0 25px rgba(255, 182, 193, 0.9) !important;
                            transform: scale(1);
                        }
                    }
                `;
                document.head.appendChild(pulseStyle);
            }

            // 设置全局变量标记阴影状态
            window.hasPinkShadow = true;
            console.log('✅ 整句粉色阴影已添加，状态已标记');
        }

        // 找到包含敏感词的完整句子并创建阴影容器
        function findCompleteSentence(highlightElement) {
            console.log('查找包含敏感词的完整句子');

            // 获取处理结果容器
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv) {
                console.log('未找到处理结果容器');
                return highlightElement.parentElement;
            }

            const fullText = processedDiv.textContent || '';
            const highlightText = highlightElement.textContent || '';
            const highlightIndex = fullText.indexOf(highlightText);

            console.log('高亮文本:', highlightText);
            console.log('在全文中的位置:', highlightIndex);

            if (highlightIndex !== -1) {
                // 向前查找句子开始（查找标点符号或段落开始）
                let sentenceStart = highlightIndex;
                for (let i = highlightIndex - 1; i >= 0; i--) {
                    const char = fullText[i];
                    if (char.match(/[。！？；\n\r]/)) {
                        sentenceStart = i + 1;
                        break;
                    }
                    if (i === 0) {
                        sentenceStart = 0;
                    }
                }

                // 向后查找句子结束
                let sentenceEnd = highlightIndex + highlightText.length;
                for (let i = sentenceEnd; i < fullText.length; i++) {
                    const char = fullText[i];
                    if (char.match(/[。！？；\n\r]/)) {
                        sentenceEnd = i + 1;
                        break;
                    }
                    if (i === fullText.length - 1) {
                        sentenceEnd = fullText.length;
                    }
                }

                // 提取完整句子文本
                const sentenceText = fullText.substring(sentenceStart, sentenceEnd).trim();
                console.log('找到完整句子文本:', sentenceText);
                console.log('句子长度:', sentenceText.length);

                // 在DOM中找到包含这个句子的最小容器
                const sentenceContainer = findSentenceContainer(processedDiv, sentenceText, highlightText);
                if (sentenceContainer) {
                    console.log('找到句子容器:', sentenceContainer.tagName);
                    return sentenceContainer;
                }
            }

            // 备用方案：返回高亮元素的父容器
            console.log('使用备用方案：高亮元素的父容器');
            return highlightElement.parentElement;
        }

        // 在DOM中找到包含句子的容器
        function findSentenceContainer(rootElement, sentenceText, highlightText) {
            console.log('在DOM中查找句子容器');

            // 遍历所有元素，找到包含完整句子的最小容器
            const walker = document.createTreeWalker(
                rootElement,
                NodeFilter.SHOW_ELEMENT,
                null,
                false
            );

            let bestContainer = null;
            let minLength = Infinity;

            let element = rootElement;
            do {
                const elementText = element.textContent || '';

                // 检查是否包含句子文本和高亮文本
                if (elementText.includes(sentenceText) ||
                    (elementText.includes(highlightText) && elementText.length < sentenceText.length * 1.5)) {

                    // 选择文本长度最接近句子长度的容器
                    if (elementText.length < minLength && elementText.length >= sentenceText.length * 0.8) {
                        bestContainer = element;
                        minLength = elementText.length;
                        console.log('找到候选容器:', element.tagName, '文本长度:', elementText.length);
                    }
                }
            } while (element = walker.nextNode());

            return bestContainer;
        }

        // 添加整句阴影覆盖效果
        function addSentenceShadow(sentenceElement) {
            console.log('添加整句阴影覆盖效果');

            // 移除之前的阴影
            removeSentenceShadow();

            // 添加阴影类
            sentenceElement.classList.add('sentence-shadow');

            // 添加阴影样式 - 完全覆盖整句
            const shadowStyle = document.createElement('style');
            shadowStyle.id = 'sentence-shadow-style';
            shadowStyle.textContent = `
                .sentence-shadow {
                    background: rgba(128, 128, 128, 0.6) !important;
                    color: rgba(255, 255, 255, 0.9) !important;
                    box-shadow: 0 0 25px rgba(128, 128, 128, 0.8) !important;
                    border-radius: 6px !important;
                    padding: 10px 12px !important;
                    margin: 6px 0 !important;
                    transition: all 0.3s ease !important;
                    animation: shadowPulse 2.5s ease-in-out infinite !important;
                    position: relative !important;
                    z-index: 10 !important;
                    display: inline-block !important;
                    line-height: 1.6 !important;
                }

                .sentence-shadow * {
                    background: transparent !important;
                    color: inherit !important;
                }

                @keyframes shadowPulse {
                    0% {
                        background: rgba(128, 128, 128, 0.6);
                        box-shadow: 0 0 25px rgba(128, 128, 128, 0.8);
                        transform: scale(1);
                    }
                    50% {
                        background: rgba(128, 128, 128, 0.75);
                        box-shadow: 0 0 35px rgba(128, 128, 128, 1);
                        transform: scale(1.02);
                    }
                    100% {
                        background: rgba(128, 128, 128, 0.6);
                        box-shadow: 0 0 25px rgba(128, 128, 128, 0.8);
                        transform: scale(1);
                    }
                }
            `;
            document.head.appendChild(shadowStyle);

            // 不再添加自动点击监听器，只有点击处理结果文本框才清除阴影
            console.log('🎯 粉色阴影已添加，只有点击处理结果文本框才会清除');

            console.log('整句阴影覆盖效果已添加');
        }

        // 移除句子阴影效果
        function removeSentenceShadow() {
            console.log('🧹 移除粉色阴影效果');
            if (window.clearPinkShadowQuick) {
                window.clearPinkShadowQuick();
            }
        }

        // 定位敏感词所在的完整句子
        function simpleHighlight(element, word) {
            console.log('simpleHighlight 被调用，敏感词:', word);

            const text = element.textContent || element.innerText || '';
            console.log('处理结果文本长度:', text.length);
            console.log('文本前100字符:', text.substring(0, 100));

            // 查找敏感词的所有位置
            const wordPositions = [];
            let index = text.indexOf(word);
            while (index !== -1) {
                wordPositions.push(index);
                index = text.indexOf(word, index + 1);
            }

            console.log('敏感词出现位置:', wordPositions);

            if (wordPositions.length === 0) {
                console.log('在处理结果中未找到敏感词');
                alert('在处理结果中未找到敏感词: ' + word);
                return;
            }

            // 获取快速检查中点击的句子
            const clickedSentence = getCurrentClickedSentence();
            console.log('点击的句子:', clickedSentence);

            let targetPosition = wordPositions[0]; // 默认使用第一个位置

            // 如果有点击的句子，尝试匹配最相似的位置
            if (clickedSentence) {
                let bestMatch = -1;
                let bestScore = 0;

                for (const pos of wordPositions) {
                    // 找到包含该敏感词的句子边界
                    let sentenceStart = 0;
                    let sentenceEnd = text.length;

                    // 向前查找句子开始
                    for (let i = pos - 1; i >= 0; i--) {
                        if (text[i] === '。' || text[i] === '！' || text[i] === '？') {
                            sentenceStart = i + 1;
                            break;
                        }
                    }

                    // 向后查找句子结束
                    for (let i = pos; i < text.length; i++) {
                        if (text[i] === '。' || text[i] === '！' || text[i] === '？') {
                            sentenceEnd = i + 1;
                            break;
                        }
                    }

                    const sentence = text.substring(sentenceStart, sentenceEnd).trim();
                    console.log(`位置 ${pos} 的句子:`, sentence);

                    // 计算相似度（简单的包含匹配）
                    const similarity = calculateSimilarity(clickedSentence, sentence);
                    console.log(`相似度: ${similarity}`);

                    if (similarity > bestScore) {
                        bestScore = similarity;
                        bestMatch = pos;
                    }
                }

                if (bestMatch !== -1) {
                    targetPosition = bestMatch;
                    console.log('选择最匹配的位置:', targetPosition);
                }
            }

            // 找到目标位置的句子边界
            let sentenceStart = 0;
            let sentenceEnd = text.length;

            for (let i = targetPosition - 1; i >= 0; i--) {
                if (text[i] === '。' || text[i] === '！' || text[i] === '？') {
                    sentenceStart = i + 1;
                    break;
                }
            }

            for (let i = targetPosition; i < text.length; i++) {
                if (text[i] === '。' || text[i] === '！' || text[i] === '？') {
                    sentenceEnd = i + 1;
                    break;
                }
            }

            const targetSentence = text.substring(sentenceStart, sentenceEnd).trim();
            console.log('目标句子:', targetSentence);

            if (!targetSentence) {
                console.log('未找到有效的目标句子');
                return;
            }

            // 高亮整个句子
            let html = element.innerHTML;
            const highlightStyle = 'background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 10px 12px; border-radius: 6px; font-weight: bold; box-shadow: 0 0 25px rgba(255, 182, 193, 0.9);';
            const escapedSentence = targetSentence.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

            // 清除之前的高亮
            html = html.replace(/<span class="sentence-highlight"[^>]*>(.*?)<\/span>/g, '$1');

            html = html.replace(new RegExp(escapedSentence, 'g'), `<span class="sentence-highlight" style="${highlightStyle}">${targetSentence}</span>`);
            element.innerHTML = html;

            console.log('句子高亮完成');

            // 滚动到高亮位置并闪动
            setTimeout(() => {
                const highlight = element.querySelector('.sentence-highlight');
                if (highlight) {
                    console.log('滚动到句子位置');
                    highlight.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 灰色闪动效果
                    setTimeout(() => {
                        highlight.style.transition = 'all 0.3s ease';
                        highlight.style.background = 'rgba(128, 128, 128, 0.6)';
                        highlight.style.boxShadow = '0 0 15px rgba(128, 128, 128, 0.8)';

                        setTimeout(() => {
                            highlight.style.background = 'rgba(128, 128, 128, 0.2)';
                            highlight.style.boxShadow = '0 0 8px rgba(128, 128, 128, 0.4)';
                        }, 400);

                        setTimeout(() => {
                            highlight.style.background = 'rgba(255, 182, 193, 0.8)';
                            highlight.style.boxShadow = '0 0 25px rgba(255, 182, 193, 0.9)';
                        }, 800);
                    }, 300);
                } else {
                    console.log('未找到高亮元素');
                }
            }, 100);
        }

        // 获取当前点击的句子
        function getCurrentClickedSentence() {
            const selectedItem = document.querySelector('.quick-check-item[style*="rgb(33, 150, 243)"]');
            if (selectedItem) {
                return decodeURIComponent(selectedItem.getAttribute('data-sentence') || '');
            }
            return '';
        }

        // 计算句子相似度
        function calculateSimilarity(sentence1, sentence2) {
            if (!sentence1 || !sentence2) return 0;

            // 简单的相似度计算：计算共同字符的比例
            const len1 = sentence1.length;
            const len2 = sentence2.length;
            let commonChars = 0;

            for (let i = 0; i < Math.min(len1, len2); i++) {
                if (sentence1[i] === sentence2[i]) {
                    commonChars++;
                } else {
                    break;
                }
            }

            // 也检查包含关系
            const contains1 = sentence2.includes(sentence1.substring(0, Math.min(20, len1)));
            const contains2 = sentence1.includes(sentence2.substring(0, Math.min(20, len2)));

            let similarity = commonChars / Math.max(len1, len2);
            if (contains1 || contains2) {
                similarity += 0.5;
            }

            return similarity;
        }



        // 直接定位敏感词并高亮所在句子
        function highlightSentenceInText(element, sensitiveWord) {
            if (!element || !sensitiveWord) {
                console.log('元素或敏感词为空');
                return;
            }

            console.log('开始定位敏感词:', sensitiveWord);

            // 清除之前的高亮
            let html = element.innerHTML;
            html = html.replace(/<span class="sentence-highlight"[^>]*>(.*?)<\/span>/g, '$1');

            // 获取纯文本内容
            const plainText = element.textContent || element.innerText || '';
            console.log('处理结果文本长度:', plainText.length);
            console.log('处理结果前100字符:', plainText.substring(0, 100));

            // 在处理结果中查找敏感词
            const sensitiveWordIndex = plainText.indexOf(sensitiveWord);
            if (sensitiveWordIndex === -1) {
                console.log('在处理结果中未找到敏感词:', sensitiveWord);
                return;
            }

            console.log('找到敏感词位置:', sensitiveWordIndex);

            // 找到包含敏感词的句子边界
            let sentenceStart = 0;
            let sentenceEnd = plainText.length;

            // 向前查找句子开始
            for (let i = sensitiveWordIndex - 1; i >= 0; i--) {
                if (plainText[i] === '。' || plainText[i] === '！' || plainText[i] === '？') {
                    sentenceStart = i + 1;
                    break;
                }
            }

            // 向后查找句子结束
            for (let i = sensitiveWordIndex; i < plainText.length; i++) {
                if (plainText[i] === '。' || plainText[i] === '！' || plainText[i] === '？') {
                    sentenceEnd = i + 1;
                    break;
                }
            }

            const targetSentence = plainText.substring(sentenceStart, sentenceEnd).trim();
            console.log('找到目标句子:', targetSentence);

            if (!targetSentence) {
                console.log('未找到有效的目标句子');
                return;
            }

            // 在HTML中查找并高亮句子
            if (html.includes(targetSentence)) {
                const style = 'background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 10px 12px; border-radius: 6px; font-weight: bold; box-shadow: 0 0 25px rgba(255, 182, 193, 0.9);';
                const escapedSentence = targetSentence.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                html = html.replace(new RegExp(escapedSentence, 'g'), `<span class="sentence-highlight" style="${style}">${targetSentence}</span>`);
                element.innerHTML = html;
                console.log('句子高亮成功');

                // 滚动并闪动
                setTimeout(() => {
                    const highlight = element.querySelector('.sentence-highlight');
                    if (highlight) {
                        console.log('开始滚动到高亮位置');
                        highlight.scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // 滚动完成后闪动
                        setTimeout(() => {
                            console.log('开始灰色闪动效果');
                            highlight.style.transition = 'all 0.3s ease';
                            highlight.style.background = 'rgba(128, 128, 128, 0.6)';
                            highlight.style.boxShadow = '0 0 15px rgba(128, 128, 128, 0.8)';

                            setTimeout(() => {
                                highlight.style.background = 'rgba(128, 128, 128, 0.2)';
                                highlight.style.boxShadow = '0 0 8px rgba(128, 128, 128, 0.4)';
                            }, 400);

                            setTimeout(() => {
                                highlight.style.background = 'rgba(255, 182, 193, 0.8)';
                                highlight.style.boxShadow = '0 0 25px rgba(255, 182, 193, 0.9)';
                                console.log('闪动效果完成');
                            }, 800);
                        }, 600);
                    } else {
                        console.log('未找到高亮元素');
                    }
                }, 100);
            } else {
                console.log('HTML中未找到目标句子');
            }
        }



        // 清除选中效果（不清除粉色阴影和敏感词高亮）
        function clearPreviousSelection_old2() {
            const processedDiv = document.getElementById('processedResult');
            if (processedDiv) {
                let html = processedDiv.innerHTML;
                html = html.replace(/<span class="sentence-highlight"[^>]*>(.*?)<\/span>/g, '$1');
                processedDiv.innerHTML = html;
            }

            // 注意：不再自动清除粉色阴影，只有点击处理结果文本框才清除
            console.log('📝 清除选中效果，保留粉色阴影');
        }









        // 超简单定位函数
        window.locateInOriginalTextByData = function(element) {
            const sentence = element.getAttribute('data-sentence');
            if (!sentence) {
                alert('没有句子数据');
                return;
            }

            const decodedSentence = decodeURIComponent(sentence);
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv) {
                alert('没有找到处理结果区域');
                return;
            }

            // 清除之前的高亮
            clearPreviousSelection();

            // 跳转到处理结果
            processedDiv.scrollIntoView({ behavior: 'auto', block: 'start' });

            // 直接高亮整句话
            const processedText = processedDiv.innerText || '';

            if (processedText.includes(decodedSentence)) {
                // 找到了完整句子，直接高亮
                highlightSentenceInText(processedDiv, decodedSentence);
            } else {
                // 找不到完整句子，尝试高亮关键词
                const words = decodedSentence.split(/[，。！？；：""''（）【】《》\s]/).filter(w => w.length > 2);
                for (let word of words) {
                    if (processedText.includes(word)) {
                        highlightSentenceInText(processedDiv, word);
                        break;
                    }
                }
            }
        }





        // 查找完整句子 - 简化高效版
        function findCompleteSentence(text, targetSentence) {
            console.log('查找完整句子:', targetSentence.substring(0, 30) + '...');

            // 首先尝试直接匹配
            if (text.includes(targetSentence)) {
                console.log('✅ 直接找到目标句子');
                return targetSentence;
            }

            // 清理标点符号进行匹配
            const cleanTarget = targetSentence.replace(/[，。！？；：""''（）【】《》\s]/g, '');
            if (cleanTarget.length >= 5 && text.includes(cleanTarget)) {
                console.log('✅ 清理后直接匹配成功');
                return cleanTarget;
            }

            // 尝试前缀匹配（从长到短）
            for (let len = Math.min(targetSentence.length, 50); len >= 8; len--) {
                const prefix = targetSentence.substring(0, len);
                if (text.includes(prefix)) {
                    console.log('✅ 前缀匹配成功，长度:', len);
                    return prefix;
                }
            }

            // 尝试后缀匹配
            for (let len = Math.min(targetSentence.length, 50); len >= 8; len--) {
                const suffix = targetSentence.substring(targetSentence.length - len);
                if (text.includes(suffix)) {
                    console.log('✅ 后缀匹配成功，长度:', len);
                    return suffix;
                }
            }

            // 尝试中间部分匹配
            if (targetSentence.length > 15) {
                const middle = targetSentence.substring(3, targetSentence.length - 3);
                if (text.includes(middle)) {
                    console.log('✅ 中间部分匹配成功');
                    return middle;
                }
            }

            console.log('❌ 未找到完整句子');
            return null;
        }

        // 查找部分匹配 - 简化高效版
        function findPartialMatch(text, targetSentence) {
            console.log('尝试部分匹配...');

            // 策略1: 智能分段匹配（从最可能的长度开始）
            const targetLen = targetSentence.length;
            const testLengths = [];

            // 生成测试长度序列：优先测试较长的有意义片段
            if (targetLen > 20) testLengths.push(Math.floor(targetLen * 0.8));
            if (targetLen > 15) testLengths.push(Math.floor(targetLen * 0.6));
            if (targetLen > 10) testLengths.push(Math.floor(targetLen * 0.5));
            testLengths.push(...[20, 15, 12, 10, 8]);

            // 去重并排序
            const uniqueLengths = [...new Set(testLengths)].filter(len => len >= 5 && len <= targetLen).sort((a, b) => b - a);

            for (let len of uniqueLengths) {
                const prefix = targetSentence.substring(0, len);
                if (text.includes(prefix)) {
                    console.log(`✅ 前${len}字符匹配成功: "${prefix}"`);
                    return prefix;
                }
            }

            // 策略2: 去除标点符号后匹配
            const cleanTarget = targetSentence.replace(/[，。！？；：""''（）【】《》\s]/g, '');
            if (cleanTarget.length >= 5) {
                // 测试清理后的不同长度
                for (let len of [cleanTarget.length, Math.floor(cleanTarget.length * 0.8), Math.floor(cleanTarget.length * 0.6)]) {
                    if (len >= 5) {
                        const cleanPrefix = cleanTarget.substring(0, len);
                        if (text.includes(cleanPrefix)) {
                            console.log(`✅ 清理后${len}字符匹配成功: "${cleanPrefix}"`);
                            return cleanPrefix;
                        }
                    }
                }
            }

            console.log('❌ 所有部分匹配策略都失败');
            return null;
        }

        // 计算文本相似度
        function calculateSimilarity(str1, str2) {
            const longer = str1.length > str2.length ? str1 : str2;
            const shorter = str1.length > str2.length ? str2 : str1;

            if (longer.length === 0) return 1.0;

            const editDistance = getEditDistance(longer, shorter);
            return (longer.length - editDistance) / longer.length;
        }

        // 计算编辑距离
        function getEditDistance(str1, str2) {
            const matrix = [];
            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }
            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }
            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }
            return matrix[str2.length][str1.length];
        }

        // 获取元素中的所有文本节点
        function getTextNodes(element) {
            const textNodes = [];
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                if (node.textContent.trim()) {
                    textNodes.push(node);
                }
            }
            return textNodes;
        }

        // 超简化高亮函数 - 无卡顿
        function highlightTextInElement(element, text, color = '#ffeb3b') {
            console.log('=== 高亮函数被调用 ===');
            console.log('要高亮的文本:', text);
            console.log('颜色:', color);

            if (!text || text.length < 2) {
                console.log('❌ 文本太短');
                return false;
            }

            let currentHTML = element.innerHTML;
            const plainText = element.innerText || '';
            console.log('元素文本长度:', plainText.length);
            console.log('元素文本前100字符:', plainText.substring(0, 100));

            if (!plainText.includes(text)) {
                console.log('❌ 文本不存在于元素中');
                return false;
            }

            console.log('✅ 文本存在，开始高亮');

            // 清除之前的高亮
            currentHTML = currentHTML.replace(/<span class="quick-highlight"[^>]*>(.*?)<\/span>/g, '$1');

            // 简单高亮样式
            const style = `background: ${color}; padding: 2px 4px; border-radius: 3px;`;

            // 直接替换
            const escapedText = text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const newHTML = currentHTML.replace(new RegExp(escapedText), `<span class="quick-highlight" style="${style}">${text}</span>`);

            if (newHTML !== currentHTML) {
                element.innerHTML = newHTML;
                console.log('✅ HTML替换成功');

                // 立即滚动到高亮位置
                const highlightedElement = element.querySelector('.quick-highlight');
                if (highlightedElement) {
                    console.log('✅ 找到高亮元素，开始滚动');
                    highlightedElement.scrollIntoView({ behavior: 'auto', block: 'center' });
                } else {
                    console.log('❌ 没有找到高亮元素');
                }
                return true;
            } else {
                console.log('❌ HTML替换失败');
                return false;
            }
        }

        // 带整句阴影的高亮函数
        function highlightTextWithSentenceShadow(element, text) {
            console.log('=== 开始带阴影的高亮文本 ===');
            console.log('要高亮的文本:', text);

            if (!text || text.length < 2) {
                console.log('❌ 文本太短，无法高亮');
                return false;
            }

            // 获取当前HTML内容和纯文本
            let currentHTML = element.innerHTML;
            const plainText = element.innerText || element.textContent || '';

            console.log('纯文本长度:', plainText.length);
            console.log('纯文本前100字符:', plainText.substring(0, 100));

            // 多种匹配策略
            let targetText = text;
            let found = false;

            // 策略1: 直接匹配
            if (plainText.includes(targetText)) {
                found = true;
                console.log('✅ 直接匹配成功');
            }
            // 策略2: 清理标点符号后匹配
            else {
                const cleanTarget = targetText.replace(/[，。！？；：""''（）【】《》\s]/g, '');
                const cleanPlain = plainText.replace(/[，。！？；：""''（）【】《》\s]/g, '');

                if (cleanPlain.includes(cleanTarget)) {
                    console.log('✅ 清理后匹配成功');
                    // 在原文中找到对应位置
                    let charCount = 0;
                    for (let i = 0; i < plainText.length; i++) {
                        const char = plainText[i];
                        if (!/[，。！？；：""''（）【】《》\s]/.test(char)) {
                            if (charCount === cleanPlain.indexOf(cleanTarget)) {
                                // 找到起始位置，提取对应长度的原文
                                let endPos = i;
                                let extractedCount = 0;
                                while (endPos < plainText.length && extractedCount < cleanTarget.length) {
                                    if (!/[，。！？；：""''（）【】《》\s]/.test(plainText[endPos])) {
                                        extractedCount++;
                                    }
                                    endPos++;
                                }
                                targetText = plainText.substring(i, endPos);
                                found = true;
                                console.log('提取的目标文本:', targetText);
                                break;
                            }
                            charCount++;
                        }
                    }
                }
            }

            // 策略3: 部分匹配
            if (!found && targetText.length > 10) {
                const partialText = targetText.substring(0, Math.floor(targetText.length * 0.8));
                if (plainText.includes(partialText)) {
                    targetText = partialText;
                    found = true;
                    console.log('✅ 部分匹配成功');
                }
            }

            // 策略4: 关键词匹配
            if (!found) {
                const words = text.split(/[，。！？；：""''（）【】《》\s]/).filter(w => w.length > 3);
                for (let word of words) {
                    if (plainText.includes(word)) {
                        targetText = word;
                        found = true;
                        console.log('✅ 关键词匹配成功:', word);
                        break;
                    }
                }
            }

            if (!found) {
                console.log('❌ 所有匹配策略都失败');
                return false;
            }

            // 清除之前的高亮和阴影
            currentHTML = currentHTML.replace(/<span class="quick-highlight"[^>]*>(.*?)<\/span>/g, '$1');
            currentHTML = currentHTML.replace(/<span class="sentence-shadow"[^>]*>(.*?)<\/span>/g, '$1');

            try {
                // 找到包含目标文本的完整句子
                const sentences = findSentenceContainingText(plainText, targetText);
                if (!sentences.completeSentence) {
                    console.log('❌ 无法找到包含目标文本的完整句子');
                    return false;
                }

                console.log('找到完整句子:', sentences.completeSentence.substring(0, 50) + '...');

                // 转义特殊字符
                const escapedText = targetText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const escapedSentence = sentences.completeSentence.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                // 创建样式 - 只使用灰色阴影，不单独高亮敏感词
                const shadowStyle = 'background: rgba(128, 128, 128, 0.15); padding: 8px 12px; border-radius: 6px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 4px 0; display: inline-block; line-height: 1.6;';

                // 为整句添加灰色阴影（包括敏感词）
                const sentenceRegex = new RegExp(escapedSentence);
                let newHTML = currentHTML.replace(sentenceRegex, `<span class="sentence-shadow" style="${shadowStyle}">${sentences.completeSentence}</span>`);

                if (newHTML !== currentHTML) {
                    element.innerHTML = newHTML;
                    console.log('✅ 带阴影的高亮成功');

                    // 立即滚动到高亮位置 - 无延迟
                    const shadowElement = element.querySelector('.sentence-shadow');
                    if (shadowElement) {
                        console.log('找到阴影元素，立即滚动');
                        shadowElement.scrollIntoView({
                            behavior: 'auto',
                            block: 'center'
                        });
                        console.log('✅ 立即滚动到阴影句子位置');
                    } else {
                        // 备选：滚动到高亮元素
                        const highlightElement = element.querySelector('.quick-highlight');
                        if (highlightElement) {
                            console.log('找到高亮元素，立即滚动');
                            highlightElement.scrollIntoView({
                                behavior: 'auto',
                                block: 'center'
                            });
                            console.log('✅ 立即滚动到高亮位置');
                        } else {
                            console.log('❌ 没有找到可滚动的元素');
                        }
                    }

                    return true;
                } else {
                    console.log('❌ HTML替换失败');
                    return false;
                }
            } catch (error) {
                console.log('❌ 带阴影高亮过程出错:', error);
                return false;
            }
        }

        // 查找包含指定文本的完整句子
        function findSentenceContainingText(fullText, targetText) {
            console.log('查找包含文本的句子:', targetText.substring(0, 20) + '...');

            // 首先尝试直接匹配
            let textIndex = fullText.indexOf(targetText);

            // 如果直接匹配失败，尝试模糊匹配
            if (textIndex === -1) {
                console.log('直接匹配失败，尝试模糊匹配');

                // 清理标点符号后匹配
                const cleanTarget = targetText.replace(/[，。！？；：""''（）【】《》\s]/g, '');
                const cleanFull = fullText.replace(/[，。！？；：""''（）【】《》\s]/g, '');
                const cleanIndex = cleanFull.indexOf(cleanTarget);

                if (cleanIndex !== -1) {
                    // 找到清理后的匹配位置，需要映射回原文位置
                    let charCount = 0;
                    for (let i = 0; i < fullText.length; i++) {
                        const char = fullText[i];
                        if (!/[，。！？；：""''（）【】《》\s]/.test(char)) {
                            if (charCount === cleanIndex) {
                                textIndex = i;
                                break;
                            }
                            charCount++;
                        }
                    }
                }

                // 如果还是找不到，尝试部分匹配
                if (textIndex === -1 && targetText.length > 10) {
                    const partialTarget = targetText.substring(0, Math.floor(targetText.length * 0.8));
                    textIndex = fullText.indexOf(partialTarget);
                    if (textIndex !== -1) {
                        console.log('部分匹配成功');
                    }
                }

                // 最后尝试关键词匹配
                if (textIndex === -1) {
                    const words = targetText.split(/[，。！？；：""''（）【】《》\s]/).filter(w => w.length > 3);
                    for (let word of words) {
                        textIndex = fullText.indexOf(word);
                        if (textIndex !== -1) {
                            console.log('关键词匹配成功:', word);
                            break;
                        }
                    }
                }
            }

            if (textIndex === -1) {
                console.log('所有匹配策略都失败');
                return { completeSentence: null, sentenceStart: -1, sentenceEnd: -1 };
            }

            // 向前查找句子开始（句号、感叹号、问号、换行符或文本开头）
            let sentenceStart = textIndex;
            while (sentenceStart > 0) {
                const char = fullText[sentenceStart - 1];
                if (char === '。' || char === '！' || char === '？' || char === '\n' || char === '\r') {
                    break;
                }
                sentenceStart--;
            }

            // 向后查找句子结束（句号、感叹号、问号、换行符或文本结尾）
            let sentenceEnd = textIndex + targetText.length;
            while (sentenceEnd < fullText.length) {
                const char = fullText[sentenceEnd];
                if (char === '。' || char === '！' || char === '？' || char === '\n' || char === '\r') {
                    sentenceEnd++;
                    break;
                }
                sentenceEnd++;
            }

            const completeSentence = fullText.substring(sentenceStart, sentenceEnd).trim();
            console.log('找到完整句子长度:', completeSentence.length);

            return {
                completeSentence: completeSentence,
                sentenceStart: sentenceStart,
                sentenceEnd: sentenceEnd
            };
        }

        // 在HTML中高亮文本
        window.highlightInHTML = function(container, text) {
            console.log('=== 开始高亮文本 ===');
            console.log('要高亮的文本:', text);
            console.log('文本长度:', text.length);
            const content = container.innerHTML;
            console.log('容器HTML内容长度:', content.length);

            // 尝试直接匹配
            if (content.includes(text)) {
                console.log('直接匹配成功，进行高亮');
                const regex = new RegExp(escapeRegExp(text), 'g');
                const newContent = content.replace(
                    regex,
                    `<span class="quick-highlight" style="background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 8px 10px; border-radius: 4px; box-shadow: 0 0 15px rgba(255,182,193,0.7);">${text}</span>`
                );
                container.innerHTML = newContent;
            } else {
                console.log('直接匹配失败，尝试忽略HTML标签匹配');
                // 如果直接匹配失败，尝试忽略HTML标签进行匹配
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                const plainText = tempDiv.textContent || tempDiv.innerText || '';

                if (plainText.includes(text)) {
                    console.log('在纯文本中找到匹配，尝试智能高亮');
                    // 找到文本在纯文本中的位置，然后在HTML中对应位置高亮
                    const textIndex = plainText.indexOf(text);
                    if (textIndex !== -1) {
                        // 简单的方法：直接在HTML中查找并替换
                        const escapedText = escapeRegExp(text);
                        const regex = new RegExp(`(>[^<]*?)${escapedText}([^<]*?<)`, 'g');
                        const newContent = content.replace(regex, `$1<span class="quick-highlight" style="background: rgba(255, 182, 193, 0.8); color: rgba(139, 69, 19, 1); padding: 8px 10px; border-radius: 4px; box-shadow: 0 0 15px rgba(255,182,193,0.7);">${text}</span>$2`);
                        if (newContent !== content) {
                            container.innerHTML = newContent;
                            console.log('智能高亮成功');
                        } else {
                            console.log('智能高亮失败，使用备用方案');
                            // 备用方案：直接在整个内容中查找替换
                            const simpleRegex = new RegExp(escapedText, 'g');
                            const simpleNewContent = content.replace(simpleRegex, `<span class="quick-highlight" style="background: #ffeb3b; padding: 2px 4px; border-radius: 3px;">${text}</span>`);
                            container.innerHTML = simpleNewContent;
                        }
                    }
                } else {
                    console.log('完全无法匹配文本');
                }
            }

            // 滚动到位置
            const highlight = container.querySelector('.quick-highlight');
            if (highlight) {
                console.log('✅ 找到高亮元素，开始滚动');
                highlight.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 添加闪烁效果提示用户
                highlight.style.animation = 'blink 1s ease-in-out 3';
                setTimeout(() => {
                    if (highlight.style) {
                        highlight.style.animation = '';
                    }
                }, 3000);
            } else {
                console.log('❌ 没有找到高亮元素');
            }
        }

        // 转义正则表达式特殊字符
        window.escapeRegExp = function(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }



        // 定位到特定的包含敏感词的句子
        function locateSpecificSentence(sentence, segmentIndex) {
            console.log('定位包含敏感词的句子:', sentence.substring(0, 50) + '...', '文本段索引:', segmentIndex);

            // 获取处理结果区域
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv || !processedDiv.innerHTML.trim()) {
                alert('请先处理文本，然后再使用定位功能');
                return;
            }

            // 清除之前的选中效果
            clearPreviousSelection();

            // 滚动到处理结果区域
            processedDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 查找包含该句子的文本节点
            const cleanSentence = sentence.replace(/<[^>]*>/g, '').trim();
            console.log('查找句子:', cleanSentence.substring(0, 50) + '...');

            // 使用文本节点遍历来精确定位
            const walker = document.createTreeWalker(
                processedDiv,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            let found = false;

            while (textNode = walker.nextNode()) {
                const nodeText = textNode.textContent;

                // 检查当前文本节点是否包含目标句子的部分内容
                if (nodeText.includes(cleanSentence) || cleanSentence.includes(nodeText.trim())) {
                    console.log('找到包含目标句子的文本节点:', nodeText.substring(0, 50) + '...');

                    // 找到包含句子的文本节点
                    const parentElement = textNode.parentElement || textNode.parentNode;

                    // 找到最合适的行级容器
                    let targetElement = parentElement;
                    while (targetElement && targetElement !== processedDiv) {
                        if (targetElement.tagName === 'DIV' || targetElement.tagName === 'P' ||
                            targetElement.style.display === 'block' ||
                            getComputedStyle(targetElement).display === 'block') {
                            break;
                        }
                        targetElement = targetElement.parentElement;
                    }

                    if (!targetElement || targetElement === processedDiv) {
                        targetElement = parentElement;
                    }

                    console.log('定位到目标元素:', targetElement.textContent.substring(0, 50) + '...');

                    // 应用选中效果
                    targetElement.classList.add('word-selected');
                    targetElement.style.backgroundColor = '#c7c7c7';
                    targetElement.style.color = '#000';
                    targetElement.style.padding = '4px 8px';
                    targetElement.style.margin = '2px 0';
                    targetElement.style.borderRadius = '2px';
                    targetElement.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                    targetElement.style.outline = '1px solid #0078d4';
                    targetElement.style.outlineOffset = '1px';
                    targetElement.contentEditable = 'true';

                    // 滚动到选中的元素
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'nearest'
                    });

                    targetElement.focus();
                    found = true;
                    break;
                }
            }

            if (!found) {
                console.log('未找到指定的句子，尝试备用方案');
                // 备用方案：按行查找
                const lines = processedDiv.innerHTML.split(/<br\s*\/?>/i);
                for (let i = 0; i < lines.length; i++) {
                    const lineText = lines[i].replace(/<[^>]*>/g, '').trim();

                    if (lineText.includes(cleanSentence) || cleanSentence.includes(lineText)) {
                        console.log('备用方案找到包含句子的行:', lineText.substring(0, 50) + '...');

                        // 创建临时容器来应用选中效果
                        const tempId = 'temp-selected-' + Date.now();
                        lines[i] = `<div id="${tempId}" class="word-selected" style="background-color: #c7c7c7; color: #000; padding: 4px 8px; margin: 2px 0; border-radius: 2px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); outline: 1px solid #0078d4; outline-offset: 1px;" contenteditable="true">${lines[i]}</div>`;

                        processedDiv.innerHTML = lines.join('<br>');

                        // 滚动到选中的行
                        const selectedElement = document.getElementById(tempId);
                        if (selectedElement) {
                            selectedElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                            selectedElement.focus();
                        }
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                console.log('未找到指定的句子');
                alert('未找到指定的句子');
            }
        }

        // 定位到特定出现位置的敏感词
        function locateSpecificOccurrence(sensitiveWord, segmentIndex) {
            console.log('定位特定出现位置的敏感词:', sensitiveWord, '文本段索引:', segmentIndex);

            // 获取处理结果区域
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv || !processedDiv.innerHTML.trim()) {
                alert('请先处理文本，然后再使用定位功能');
                return;
            }

            // 清除之前的选中效果
            clearPreviousSelection();

            // 滚动到处理结果区域
            processedDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 查找包含敏感词的特定文本段
            const cleanSensitiveWord = sensitiveWord.replace(/<[^>]*>/g, '').trim();
            console.log('查找敏感词:', cleanSensitiveWord, '在第', segmentIndex, '个文本段中');

            // 创建敏感词的正则表达式
            const sensitiveWordRegex = new RegExp(sensitiveProcessor.escapeRegExp(cleanSensitiveWord), 'gi');

            // 使用文本节点遍历来精确定位
            const walker = document.createTreeWalker(
                processedDiv,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let textNode;
            let found = false;
            let currentSegmentIndex = 0;

            while (textNode = walker.nextNode()) {
                const nodeText = textNode.textContent;

                // 检查当前文本节点是否包含敏感词
                if (sensitiveWordRegex.test(nodeText)) {
                    // 如果是我们要找的特定出现位置
                    if (currentSegmentIndex === segmentIndex) {
                        console.log('找到目标出现位置的文本节点:', nodeText);

                        // 找到包含敏感词的文本节点
                        const parentElement = textNode.parentElement || textNode.parentNode;

                        // 找到最合适的行级容器
                        let targetElement = parentElement;
                        while (targetElement && targetElement !== processedDiv) {
                            if (targetElement.tagName === 'DIV' || targetElement.tagName === 'P' ||
                                targetElement.style.display === 'block' ||
                                getComputedStyle(targetElement).display === 'block') {
                                break;
                            }
                            targetElement = targetElement.parentElement;
                        }

                        if (!targetElement || targetElement === processedDiv) {
                            targetElement = parentElement;
                        }

                        console.log('定位到目标元素:', targetElement.textContent);

                        // 应用选中效果
                        targetElement.classList.add('word-selected');
                        targetElement.style.backgroundColor = '#c7c7c7';
                        targetElement.style.color = '#000';
                        targetElement.style.padding = '4px 8px';
                        targetElement.style.margin = '2px 0';
                        targetElement.style.borderRadius = '2px';
                        targetElement.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                        targetElement.style.outline = '1px solid #0078d4';
                        targetElement.style.outlineOffset = '1px';
                        targetElement.contentEditable = 'true';

                        // 滚动到选中的元素
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });

                        targetElement.focus();
                        found = true;
                        break;
                    }
                    currentSegmentIndex++;
                }
            }

            if (!found) {
                console.log('未找到指定位置的敏感词');
                alert('未找到指定位置的敏感词');
            }
        }

        // 定位到处理结果中的对应敏感词（不关闭弹窗，Word风格选中）
        function locateInProcessedResult(sensitiveWord, wordIndex) {
            console.log('定位敏感词:', sensitiveWord, '索引:', wordIndex);

            // 不关闭弹窗，保持弹窗打开状态

            // 获取处理结果区域
            const processedDiv = document.getElementById('processedResult');
            if (!processedDiv || !processedDiv.innerHTML.trim()) {
                alert('请先处理文本，然后再使用定位功能');
                return;
            }

            // 清除之前的选中效果
            clearPreviousSelection();

            // 滚动到处理结果区域
            processedDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 查找包含敏感词的句子或段落
            const cleanSensitiveWord = sensitiveWord.replace(/<[^>]*>/g, '').trim();
            console.log('查找敏感词:', cleanSensitiveWord);

            // 获取处理结果的文本内容
            const processedText = processedDiv.textContent || processedDiv.innerText;

            // 创建敏感词的正则表达式（支持单字和多字）
            const sensitiveWordRegex = new RegExp(sensitiveProcessor.escapeRegExp(cleanSensitiveWord), 'gi');

            if (sensitiveWordRegex.test(processedText)) {
                // 使用文本节点遍历来精确定位包含敏感词的句子
                const walker = document.createTreeWalker(
                    processedDiv,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let textNode;
                let found = false;

                while (textNode = walker.nextNode()) {
                    const nodeText = textNode.textContent;

                    // 检查当前文本节点是否包含敏感词
                    if (sensitiveWordRegex.test(nodeText)) {
                        console.log('找到包含敏感词的文本节点:', nodeText);

                        // 找到包含敏感词的文本节点
                        const parentElement = textNode.parentElement || textNode.parentNode;

                        // 找到最合适的行级容器（句子级别）
                        let targetElement = parentElement;
                        while (targetElement && targetElement !== processedDiv) {
                            if (targetElement.tagName === 'DIV' || targetElement.tagName === 'P' ||
                                targetElement.style.display === 'block' ||
                                getComputedStyle(targetElement).display === 'block') {
                                break;
                            }
                            targetElement = targetElement.parentElement;
                        }

                        // 如果没找到合适的块级元素，就用父元素
                        if (!targetElement || targetElement === processedDiv) {
                            targetElement = parentElement;
                        }

                        console.log('定位到目标元素:', targetElement.textContent);

                        // 应用整个句子的阴影效果
                        targetElement.classList.add('word-selected');
                        targetElement.style.backgroundColor = '#c7c7c7';
                        targetElement.style.color = '#000';
                        targetElement.style.padding = '4px 8px';
                        targetElement.style.margin = '2px 0';
                        targetElement.style.borderRadius = '2px';
                        targetElement.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                        targetElement.style.outline = '1px solid #0078d4';
                        targetElement.style.outlineOffset = '1px';

                        // 设置可编辑
                        targetElement.contentEditable = 'true';

                        // 立即滚动到选中的元素（无延迟）
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });

                        // 立即聚焦（无延迟）
                        targetElement.focus();

                        found = true;
                        break;
                    }
                }

                if (!found) {
                    // 备用方案：按行查找包含敏感词的行
                    console.log('使用备用方案查找敏感词');
                    const lines = processedDiv.innerHTML.split(/<br\s*\/?>/i);
                    for (let i = 0; i < lines.length; i++) {
                        const lineText = lines[i].replace(/<[^>]*>/g, '').trim();

                        // 使用正则表达式检查是否包含敏感词
                        if (sensitiveWordRegex.test(lineText)) {
                            console.log('备用方案找到包含敏感词的行:', lineText);

                            // 创建临时容器来应用选中效果
                            const tempId = 'temp-selected-' + Date.now();
                            lines[i] = `<div id="${tempId}" class="word-selected" style="background-color: #c7c7c7; color: #000; padding: 4px 8px; margin: 2px 0; border-radius: 2px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); outline: 1px solid #0078d4; outline-offset: 1px;" contenteditable="true">${lines[i]}</div>`;

                            processedDiv.innerHTML = lines.join('<br>');

                            // 立即滚动到选中的行（无延迟）
                            const selectedElement = document.getElementById(tempId);
                            if (selectedElement) {
                                selectedElement.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });

                                // 立即聚焦（无延迟）
                                selectedElement.focus();
                            }
                            found = true;
                            break;
                        }
                    }
                }

                if (!found) {
                    console.log('未找到包含敏感词的句子:', cleanSensitiveWord);
                    // 简单提示
                    processedDiv.style.backgroundColor = '#fff3cd';
                    processedDiv.style.border = '2px solid #ffc107';
                    setTimeout(() => {
                        processedDiv.style.backgroundColor = '';
                        processedDiv.style.border = '';
                    }, 1000);
                }
            } else {
                console.log('处理结果中不包含敏感词:', cleanSensitiveWord);
                alert('在处理结果中未找到该敏感词，可能已被处理或不存在');
            }
        }

        // 清除之前的选中效果
        function clearPreviousSelection() {
            const processedDiv = document.getElementById('processedResult');

            // 清除word-selected类的元素
            const selectedElements = processedDiv.querySelectorAll('.word-selected');
            selectedElements.forEach(element => {
                element.classList.remove('word-selected');
                element.style.backgroundColor = '';
                element.style.color = '';
                element.style.padding = '';
                element.style.margin = '';
                element.style.borderRadius = '';
                element.style.boxShadow = '';
                element.style.outline = '';
                element.style.outlineOffset = '';
                element.contentEditable = 'inherit';
            });

            // 注意：不清除quick-highlight类的元素，保留敏感词高亮
            // 敏感词高亮应该保持显示，只有重新处理文本时才清除

            // 清除sentence-shadow类的元素（但保留粉色阴影）
            const sentenceShadows = processedDiv.querySelectorAll('.sentence-shadow');
            sentenceShadows.forEach(element => {
                // 将阴影元素的内容替换回原文本
                const parent = element.parentNode;
                const textNode = document.createTextNode(element.textContent);
                parent.replaceChild(textNode, element);
            });

            // 合并相邻的文本节点
            processedDiv.normalize();

            // 注意：不再自动清除粉色阴影和敏感词高亮，只有点击处理结果文本框才清除阴影
            console.log('📝 清除其他选中效果，保留粉色阴影和敏感词高亮');
        }



        // 处理处理结果区域的聚焦事件
        function handleProcessedResultFocus(event) {
            // 聚焦时清除选中效果但保持弹窗打开
            clearPreviousSelection();
            console.log('聚焦清除选中效果，保持弹窗打开');
        }

        // 全局点击事件监听器 - 点击任意地方清除阴影
        document.addEventListener('click', function(event) {
            // 如果点击的不是快速检查弹窗内部，清除选中效果
            const quickCheckModal = document.getElementById('quickCheckModal');
            const isClickInsideModal = quickCheckModal && quickCheckModal.contains(event.target);

            // 如果点击的是快速检查项，不要清除选中效果
            const isQuickCheckItem = event.target.classList.contains('quick-check-item') ||
                                   event.target.closest('.quick-check-item');

            if (!isClickInsideModal && !isQuickCheckItem) {
                clearPreviousSelection();
            }
        });

        // 转义正则表达式特殊字符
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // 复制检查结果
        function copyCheckResults() {
            const resultsDiv = document.getElementById('quickCheckResults');
            const textContent = resultsDiv.textContent || resultsDiv.innerText;

            if (textContent.trim()) {
                navigator.clipboard.writeText(textContent).then(() => {
                    alert('检查结果已复制到剪贴板');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = textContent;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('检查结果已复制到剪贴板');
                });
            } else {
                alert('没有可复制的检查结果');
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('JavaScript错误:', event.error);
            console.error('错误位置:', event.filename, '行:', event.lineno);
        });

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            const originalText = document.getElementById('originalText');

            // 初始化界面
            updateSensitiveWordsList();
            updateCategoryToggles();
            updateCharacterCount();

            // 监听导出选项变化
            document.getElementById('preserveFormat').addEventListener('change', previewExport);
            document.getElementById('fileName').addEventListener('input', previewExport);

            // 监听输入变化
            originalText.addEventListener('input', function() {
                // 更新字符计数
                updateCharacterCount();

                // 清除之前的定时器
                if (autoProcessTimer) {
                    clearTimeout(autoProcessTimer);
                }

                // 300ms后自动处理
                autoProcessTimer = setTimeout(() => {
                    if (originalText.value.trim()) {
                        processText();
                    }
                }, 300);
            });

            // 监听粘贴事件，自动处理
            originalText.addEventListener('paste', function(e) {
                setTimeout(() => {
                    updateCharacterCount();
                    processText();
                }, 100);
            });

            // 为处理结果区域绑定优化的点击事件
            const processedDiv = document.getElementById('processedResult');
            if (processedDiv) {
                let clickTimeout;

                // 防抖的点击监听器
                processedDiv.addEventListener('click', function(event) {
                    // 清除之前的超时
                    if (clickTimeout) {
                        clearTimeout(clickTimeout);
                    }

                    // 延迟执行，避免频繁触发
                    clickTimeout = setTimeout(() => {
                        console.log('🖱️ 处理结果文本框点击');

                        // 只清除定位阴影（浅橙色背景+左橙色边框），保留敏感词高亮
                        clearPreviousLocationHighlight();

                        // 清除粉色阴影
                        if (window.hasPinkShadow) {
                            window.clearPinkShadow();
                        }

                        // 注意：不调用clearPreviousSelection()，保留敏感词和符号的高亮
                    }, 50); // 50ms防抖
                });

                console.log('🎯 已绑定优化的点击事件');
            } else {
                console.log('❌ 未找到处理结果区域');
            }

            // 添加快速粉色阴影清除函数
            window.clearPinkShadow = function() {
                console.log('🌸 快速清除粉色阴影');
                clearPinkShadowQuick();
            };

            // 超快速清除函数（性能优化）
            window.clearPinkShadowQuick = function() {
                if (window.hasPinkShadow) {
                    // 批量处理 - 减少DOM查询
                    const shadowElements = document.querySelectorAll('.pink-shadow-active');
                    const oldShadowElements = document.querySelectorAll('.sentence-shadow-pulse');

                    // 使用requestAnimationFrame确保流畅性
                    requestAnimationFrame(() => {
                        // 快速移除类名
                        shadowElements.forEach(element => {
                            element.classList.remove('pink-shadow-active');

                            // 快速恢复显示类型
                            if (element.hasAttribute('data-original-display')) {
                                element.style.display = '';
                                element.removeAttribute('data-original-display');
                            }
                        });

                        // 清理旧阴影
                        oldShadowElements.forEach(element => {
                            element.classList.remove('sentence-shadow-pulse');
                        });

                        // 重置状态
                        window.hasPinkShadow = false;
                        console.log('✅ 粉色阴影已超快速清除');
                    });
                }
            };

            console.log('🎯 高性能粉色阴影系统已就绪 - 流畅无卡顿');















        });
        

        

        
        function processText() {
            console.log('🚀 processText 函数被调用');

            const originalTextElement = document.getElementById('originalText');
            console.log('原始文本元素:', originalTextElement);

            if (!originalTextElement) {
                console.error('❌ 未找到原始文本输入框');
                alert('错误：未找到原始文本输入框');
                return;
            }

            const originalContent = originalTextElement.value.trim();
            console.log('📝 获取到的原始内容:', originalContent);
            console.log('📏 原始内容长度:', originalContent.length);

            if (!originalContent) {
                console.log('⚠️ 原始内容为空，显示默认消息');
                const processedResultElement = document.getElementById('processedResult');
                if (processedResultElement) {
                    processedResultElement.innerHTML = '处理结果将在这里显示...';
                    console.log('✅ 默认消息已显示');
                } else {
                    console.error('❌ 未找到处理结果显示区域');
                }
                return;
            }

            let processed = originalContent;

            // 调试：显示原始内容
            console.log('开始处理，原始内容:', originalContent);

            // 第一步：去除数字序号（修复：更精确的匹配）
            const beforeStep1 = processed;
            processed = processed.replace(/^\s*\d+\s*$/gm, ''); // 只删除纯数字行
            processed = processed.replace(/^\s*\d+[.、]\s*(?=\S)/gm, ''); // 只删除数字序号，保留后面的内容
            console.log('步骤1 - 去除数字序号前:', beforeStep1);
            console.log('步骤1 - 去除数字序号后:', processed);

            // 第二步：处理标点符号
            const beforeStep2 = processed;
            // 1. 删除对话符
            processed = processed.replace(/[「」]/g, '');

            // 2. 处理标点符号（省略号不分行）
            processed = processed.replace(/[，。]/g, '\n');  // 删除逗号、句号并换行
            processed = processed.replace(/\.\.\./g, '\n');   // 删除英文省略号并换行
            processed = processed.replace(/？/g, '？\n');     // 保留问号并换行
            processed = processed.replace(/！/g, '！\n');     // 保留感叹号并换行
            processed = processed.replace(/：/g, '：\n');     // 保留冒号并换行
            processed = processed.replace(/[【】]/g, '\n');   // 删除中括号并换行
            console.log('步骤2 - 处理标点符号前:', beforeStep2);
            console.log('步骤2 - 处理标点符号后:', processed);

            // 第三步：分割并清理空行
            const beforeStep3 = processed;
            const lines = processed.split('\n');
            const cleanedLines = [];

            for (let line of lines) {
                line = line.trim();
                if (line.length > 0) {
                    cleanedLines.push(line);
                }
            }
            console.log('步骤3 - 清理空行前:', beforeStep3);
            console.log('步骤3 - 分割后的行:', lines);
            console.log('步骤3 - 清理后的行:', cleanedLines);

            // 第四步：转换HTML并高亮
            processed = cleanedLines.join('<br><br>');

            // 高亮所有符号（黄色背景红字）
            processed = processed.replace(/？/g, '<span class="highlight-symbol">？</span>');
            processed = processed.replace(/！/g, '<span class="highlight-symbol">！</span>');
            processed = processed.replace(/：/g, '<span class="highlight-symbol">：</span>');
            processed = processed.replace(/…/g, '<span class="highlight-symbol">…</span>');

            // 第五步：敏感词处理（替换和高亮）
            console.log('开始敏感词处理，sensitiveProcessor:', sensitiveProcessor);
            try {
                const sensitiveResult = sensitiveProcessor.processText(processed, false); // false表示进行替换和高亮
                console.log('敏感词处理结果:', sensitiveResult);
                processed = sensitiveResult.processedText;
                console.log('步骤5 - 敏感词处理后:', processed);
            } catch (error) {
                console.error('敏感词处理出错:', error);
                console.log('跳过敏感词处理，使用原始处理结果');
            }

            // 显示结果
            console.log('📋 准备显示最终结果');
            console.log('📄 处理后的内容长度:', processed.length);
            console.log('📄 处理后的内容预览:', processed.substring(0, 200) + '...');

            const processedResultElement = document.getElementById('processedResult');
            if (processedResultElement) {
                processedResultElement.innerHTML = processed;

                // 清除定位缓存，确保新内容的定位功能正常
                clearLocationCache();

                console.log('✅ 结果已成功显示到页面');
                console.log('📊 显示区域内容长度:', processedResultElement.innerHTML.length);
            } else {
                console.error('❌ 未找到处理结果显示区域 #processedResult');
                alert('错误：未找到处理结果显示区域');
            }

            // 更新字符计数
            updateCharacterCount();
        }
        
        function copyResult() {
            const resultDiv = document.getElementById('processedResult');

            // 检查是否有内容
            const textContent = resultDiv.textContent || resultDiv.innerText;
            if (!textContent || textContent === '处理结果将在这里显示...') {
                alert('没有可复制的内容');
                return;
            }

            // 获取HTML内容并处理格式
            let htmlContent = resultDiv.innerHTML;

            // 创建临时容器来处理内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 去除所有高亮相关的样式和类
            const highlightedElements = tempDiv.querySelectorAll('.word-selected, [style*="background"], [style*="color"]');
            highlightedElements.forEach(element => {
                // 移除高亮类
                element.classList.remove('word-selected');

                // 移除高亮相关的样式
                element.style.backgroundColor = '';
                element.style.color = '';
                element.style.padding = '';
                element.style.margin = '';
                element.style.borderRadius = '';
                element.style.boxShadow = '';
                element.style.outline = '';
                element.style.outlineOffset = '';

                // 如果元素没有其他样式，移除style属性
                if (!element.style.cssText) {
                    element.removeAttribute('style');
                }

                // 如果元素没有其他类，移除class属性
                if (!element.className) {
                    element.removeAttribute('class');
                }
            });

            // 将HTML转换为带格式的文本
            let formattedContent = tempDiv.innerHTML;

            // 处理换行：将<br>转换为换行符
            formattedContent = formattedContent.replace(/<br\s*\/?>/gi, '\n');

            // 处理段落：将</p>转换为双换行
            formattedContent = formattedContent.replace(/<\/p>/gi, '\n\n');
            formattedContent = formattedContent.replace(/<p[^>]*>/gi, '');

            // 处理div：将</div>转换为换行
            formattedContent = formattedContent.replace(/<\/div>/gi, '\n');
            formattedContent = formattedContent.replace(/<div[^>]*>/gi, '');

            // 移除所有剩余的HTML标签
            formattedContent = formattedContent.replace(/<[^>]*>/g, '');

            // 解码HTML实体
            const tempTextDiv = document.createElement('div');
            tempTextDiv.innerHTML = formattedContent;
            formattedContent = tempTextDiv.textContent || tempTextDiv.innerText;

            // 清理多余的空行（保留段落间的双换行）
            formattedContent = formattedContent.replace(/\n{3,}/g, '\n\n');
            formattedContent = formattedContent.trim();

            // 创建带格式的HTML内容，使用与纯净Word相同的字体格式
            const formattedHTML = `
                <div style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12pt; line-height: 1.8;">
                    ${formattedContent.split('\n\n').map(paragraph =>
                        paragraph.trim() ? `<p style="margin: 0 0 6pt 0; padding: 0; text-indent: 2em;">${paragraph.trim()}</p>` : ''
                    ).filter(p => p).join('')}
                </div>
            `;

            // 尝试复制带格式的内容到剪贴板
            if (navigator.clipboard && navigator.clipboard.write) {
                const htmlBlob = new Blob([formattedHTML], { type: 'text/html' });
                const textBlob = new Blob([formattedContent], { type: 'text/plain' });

                navigator.clipboard.write([
                    new ClipboardItem({
                        'text/html': htmlBlob,
                        'text/plain': textBlob
                    })
                ]).then(() => {
                    alert('已复制到剪贴板');
                }).catch(() => {
                    // 如果带格式复制失败，回退到纯文本
                    navigator.clipboard.writeText(formattedContent).then(() => {
                        alert('已复制到剪贴板');
                    }).catch(() => {
                        // 最终备用方案
                        const textArea = document.createElement('textarea');
                        textArea.value = formattedContent;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        alert('已复制到剪贴板');
                    });
                });
            } else {
                // 浏览器不支持新的Clipboard API，使用传统方法
                navigator.clipboard.writeText(formattedContent).then(() => {
                    alert('已复制到剪贴板');
                }).catch(() => {
                    // 备用方案
                    const textArea = document.createElement('textarea');
                    textArea.value = formattedContent;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('已复制到剪贴板');
                });
            }
        }
        


        function clearAll() {
            document.getElementById('originalText').value = '';
            document.getElementById('processedResult').innerHTML = '处理结果将在这里显示...';
        }
        






        // 分析文本中的标记块
        function analyzeMarkedBlocks(text) {
            const lines = text.split('\n');
            const blocks = {
                numbers: [],
                dialogs: [],
                sensitive: [],
                english: [],
                total: 0
            };

            let currentPos = 0;

            lines.forEach((line, lineIndex) => {
                const trimmedLine = line.trim();
                if (!trimmedLine) {
                    currentPos += line.length + 1; // +1 for newline
                    return;
                }

                const lineStart = currentPos;
                const lineEnd = currentPos + line.length;

                // 检查数字序号
                if (/^\s*\d+[.、]\s*/.test(line) || /^\s*\d+\s*$/.test(line)) {
                    blocks.numbers.push({
                        content: trimmedLine,
                        preview: getContextPreview(text, lineStart, lineEnd),
                        start: lineStart,
                        end: lineEnd,
                        line: lineIndex + 1
                    });
                }

                // 检查对话符号
                if (/[「」]/.test(line)) {
                    const matches = [...line.matchAll(/[「」]/g)];
                    matches.forEach(match => {
                        const matchStart = lineStart + match.index;
                        const matchEnd = matchStart + 1;
                        blocks.dialogs.push({
                            content: `对话符号: ${match[0]}`,
                            preview: getContextPreview(text, matchStart, matchEnd),
                            start: matchStart,
                            end: matchEnd,
                            line: lineIndex + 1
                        });
                    });
                }

                // 检查敏感词
                for (const [word, info] of sensitiveProcessor.sensitiveWords) {
                    const regex = new RegExp(word, 'gi');
                    const matches = [...line.matchAll(regex)];
                    matches.forEach(match => {
                        const matchStart = lineStart + match.index;
                        const matchEnd = matchStart + match[0].length;
                        blocks.sensitive.push({
                            content: `敏感词: ${match[0]}`,
                            preview: getContextPreview(text, matchStart, matchEnd),
                            start: matchStart,
                            end: matchEnd,
                            line: lineIndex + 1
                        });
                    });
                }

                // 检查英文标点
                if (/[,.!?;:]/.test(line)) {
                    const matches = [...line.matchAll(/[,.!?;:]/g)];
                    matches.forEach(match => {
                        const matchStart = lineStart + match.index;
                        const matchEnd = matchStart + 1;
                        blocks.english.push({
                            content: `英文标点: ${match[0]}`,
                            preview: getContextPreview(text, matchStart, matchEnd),
                            start: matchStart,
                            end: matchEnd,
                            line: lineIndex + 1
                        });
                    });
                }

                currentPos += line.length + 1; // +1 for newline
            });

            blocks.total = blocks.numbers.length + blocks.dialogs.length +
                          blocks.sensitive.length + blocks.english.length;

            return blocks;
        }

        // 获取上下文预览
        function getContextPreview(text, start, end) {
            const contextLength = 30;
            const beforeStart = Math.max(0, start - contextLength);
            const afterEnd = Math.min(text.length, end + contextLength);

            let preview = text.substring(beforeStart, afterEnd);

            // 添加省略号
            if (beforeStart > 0) preview = '...' + preview;
            if (afterEnd < text.length) preview = preview + '...';

            // 高亮目标文本
            const targetText = text.substring(start, end);
            const relativeStart = start - beforeStart + (beforeStart > 0 ? 3 : 0);
            const relativeEnd = relativeStart + targetText.length;

            preview = preview.substring(0, relativeStart) +
                     `<strong style="background: #ffeb3b;">${targetText}</strong>` +
                     preview.substring(relativeEnd);

            return preview;
        }

        // 定位到文本位置
        function locateText(start, end) {
            const textarea = document.getElementById('originalText');
            textarea.focus();
            textarea.setSelectionRange(start, end);
            textarea.scrollTop = textarea.scrollHeight * (start / textarea.value.length);
        }

        function copyOriginal() {
            const text = document.getElementById('originalText').value;
            if (!text.trim()) {
                alert('没有可复制的原文内容');
                return;
            }

            navigator.clipboard.writeText(text).then(() => {
                alert('原文已复制到剪贴板');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('原文已复制到剪贴板');
            });
        }

        function toggleFindReplace() {
            const panel = document.getElementById('findReplacePanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        function toggleSplitText() {
            const panel = document.getElementById('splitTextPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';

            // 监听拆分方法变化
            const splitMethod = document.getElementById('splitMethod');
            const customSeparator = document.getElementById('customSeparator');
            const splitValue = document.getElementById('splitValue');

            splitMethod.onchange = function() {
                if (this.value === 'custom') {
                    customSeparator.style.display = 'block';
                    splitValue.style.display = 'none';
                } else {
                    customSeparator.style.display = 'none';
                    splitValue.style.display = 'block';
                }
            };
        }

        // 敏感词管理功能
        function toggleSensitivePanel() {
            const panel = document.getElementById('sensitivePanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }



        function addCustomWord() {
            const wordInput = document.getElementById('newSensitiveWord');
            const replacementInput = document.getElementById('newReplacement');

            const word = wordInput.value.trim();
            let replacement = replacementInput.value.trim();

            if (!word) {
                alert('请输入敏感词');
                return;
            }

            // 如果没有输入替换词，设置为特殊标记表示只高亮
            if (!replacement) {
                replacement = '__HIGHLIGHT_ONLY__';
            }

            sensitiveProcessor.addCustomWord(word, replacement);
            wordInput.value = '';
            replacementInput.value = '';
            updateSensitiveWordsList();

            const action = replacement === '__HIGHLIGHT_ONLY__' ? '(仅高亮)' : '(替换)';
            alert(`敏感词添加成功 ${action}`);
        }

        function updateSensitiveWordsList() {
            const listDiv = document.getElementById('sensitiveWordsList');
            listDiv.innerHTML = '';

            let count = 0;
            for (const [word, info] of sensitiveProcessor.sensitiveWords) {
                if (count >= 15) break;

                const item = document.createElement('div');
                item.className = 'sensitive-item';
                item.innerHTML = `
                    <span>${word} → ${info.replacement}</span>
                    <button class="remove-btn" onclick="removeWord('${word}')">删除</button>
                `;
                listDiv.appendChild(item);
                count++;
            }

            if (sensitiveProcessor.sensitiveWords.size > 15) {
                const moreItem = document.createElement('div');
                moreItem.style.textAlign = 'center';
                moreItem.style.color = '#666';
                moreItem.style.fontSize = '10px';
                moreItem.textContent = `还有 ${sensitiveProcessor.sensitiveWords.size - 15} 个词汇...`;
                listDiv.appendChild(moreItem);
            }
        }

        function removeWord(word) {
            if (confirm(`确定要删除敏感词"${word}"吗？`)) {
                sensitiveProcessor.removeWord(word);
                updateSensitiveWordsList();
            }
        }



        // 优化的弹窗控制函数 - 提升响应速度
        function openModal(modalId) {
            const modal = document.getElementById(modalId);

            // 立即显示弹窗，不等待任何处理
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // 使用requestAnimationFrame优化后续处理时机
            requestAnimationFrame(() => {
                // 异步初始化内容，避免阻塞弹窗显示
                if (modalId === 'sensitiveWordsModal') {
                    // 减少延迟，提升响应速度
                    setTimeout(() => {
                        updateModalSensitiveWordsList();
                    }, 5);
                }

                // 快速检查弹窗优化
                if (modalId === 'quickCheckModal') {
                    // 大幅减少延迟时间
                    setTimeout(() => {
                        performSensitiveCheck();
                    }, 20);
                }

                // 拆分文本弹窗优化
                if (modalId === 'splitTextModal') {
                    // 减少延迟时间
                    setTimeout(() => {
                        autoSplitAndShow();
                    }, 10);
                }
            });
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        // 专门用于关闭快速检查弹窗的函数
        window.closeQuickCheckModal = function() {
            // 清除所有选中效果
            clearPreviousSelection();
            // 关闭弹窗
            closeModal('quickCheckModal');
        }

        // 刷新敏感词检查的函数
        window.refreshSensitiveCheck = function() {
            console.log('🔄 刷新快速检查');

            // 清除所有缓存和状态
            lastQuickCheckText = '';
            quickCheckCache = null;
            window.cachedSensitiveWords = null;

            // 清除之前的选中效果和粉色阴影
            clearPreviousSelection();
            if (window.hasPinkShadow) {
                window.clearPinkShadowQuick();
            }

            // 显示加载状态
            const resultsDiv = document.getElementById('quickCheckResults');
            if (resultsDiv) {
                resultsDiv.innerHTML = `
                    <div style="text-align: center; color: #6c757d; padding: 20px;">
                        <div style="font-size: 20px; margin-bottom: 6px; opacity: 0.6;">🔄</div>
                        <div style="font-size: 12px;">正在重新检查敏感词...</div>
                    </div>
                `;
            }

            // 立即执行检查
            setTimeout(() => {
                performSensitiveCheck();
            }, 100);
        }

        // 点击弹窗外部关闭（快速检查弹窗除外）
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                // 如果是快速检查弹窗，不要关闭
                if (event.target.id === 'quickCheckModal') {
                    return;
                }
                closeModal(event.target.id);
            }
        }

        // ESC键关闭弹窗（快速检查弹窗除外）
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        // 如果是快速检查弹窗，不要关闭
                        if (modal.id === 'quickCheckModal') {
                            return;
                        }
                        closeModal(modal.id);
                    }
                });
            }
        });













        // 查找替换功能
        let currentSearchIndex = -1;
        let searchMatches = [];

        function findNext() {
            const findText = document.getElementById('findText').value;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const useRegex = document.getElementById('useRegex').checked;
            const resultDiv = document.getElementById('findResult');

            if (!findText) {
                resultDiv.textContent = '请输入要查找的内容';
                return;
            }

            const textarea = document.getElementById('originalText');
            const text = textarea.value;

            try {
                let regex;
                if (useRegex) {
                    regex = new RegExp(findText, caseSensitive ? 'g' : 'gi');
                } else {
                    const escapedText = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    regex = new RegExp(escapedText, caseSensitive ? 'g' : 'gi');
                }

                searchMatches = [];
                let match;
                while ((match = regex.exec(text)) !== null) {
                    searchMatches.push({
                        index: match.index,
                        length: match[0].length,
                        text: match[0]
                    });
                    if (!regex.global) break;
                }

                if (searchMatches.length === 0) {
                    resultDiv.textContent = '未找到匹配项';
                    return;
                }

                currentSearchIndex = (currentSearchIndex + 1) % searchMatches.length;
                const currentMatch = searchMatches[currentSearchIndex];

                textarea.focus();
                textarea.setSelectionRange(currentMatch.index, currentMatch.index + currentMatch.length);

                resultDiv.textContent = `找到 ${searchMatches.length} 个匹配项，当前第 ${currentSearchIndex + 1} 个`;

            } catch (error) {
                resultDiv.textContent = '正则表达式语法错误';
            }
        }

        function replaceAll() {
            const findText = document.getElementById('findText').value;
            const replaceText = document.getElementById('replaceText').value;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const useRegex = document.getElementById('useRegex').checked;
            const resultDiv = document.getElementById('findResult');

            if (!findText) {
                resultDiv.textContent = '请输入要查找的内容';
                return;
            }

            const textarea = document.getElementById('originalText');
            let text = textarea.value;

            try {
                let regex;
                if (useRegex) {
                    regex = new RegExp(findText, caseSensitive ? 'g' : 'gi');
                } else {
                    const escapedText = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    regex = new RegExp(escapedText, caseSensitive ? 'g' : 'gi');
                }

                const matches = text.match(regex);
                if (!matches) {
                    resultDiv.textContent = '未找到匹配项';
                    return;
                }

                text = text.replace(regex, replaceText);
                textarea.value = text;

                resultDiv.textContent = `已替换 ${matches.length} 个匹配项`;

                // 自动处理文本
                setTimeout(() => {
                    processText();
                }, 100);

            } catch (error) {
                resultDiv.textContent = '正则表达式语法错误';
            }
        }

        function updateCategoryToggles() {
            const togglesDiv = document.getElementById('categoryToggles');
            togglesDiv.innerHTML = '';

            const categories = {
                'political': '政治敏感',
                'violence': '暴力犯罪',
                'adult': '色情内容',
                'drugs': '毒品相关',
                'gambling': '赌博相关',
                'relationship': '情感关系',
                'vulgar': '粗俗词汇'
            };

            for (const [key, name] of Object.entries(categories)) {
                const toggle = document.createElement('div');
                toggle.className = 'category-toggle';
                toggle.innerHTML = `
                    <input type="checkbox" id="cat_${key}" checked onchange="toggleCategory('${key}')">
                    <label for="cat_${key}">${name}</label>
                `;
                togglesDiv.appendChild(toggle);
            }
        }

        function toggleCategory(categoryName) {
            const checkbox = document.getElementById(`cat_${categoryName}`);
            const category = sensitiveProcessor.categories.get(categoryName);
            if (category) {
                category.enabled = checkbox.checked;
            }
        }

        // 此功能已移除 - 保护用户自定义敏感词不被清空
        function resetDefaultSensitive() {
            alert('🔒 此功能已禁用\n\n为保护您的自定义敏感词设置，不提供清空功能。\n如需删除特定敏感词，请在敏感词列表中逐个删除。');
        }

        // 查找替换功能
        let currentFindIndex = -1;
        let findMatches = [];

        function findNext() {
            const findText = document.getElementById('findText').value;
            const originalText = document.getElementById('originalText');
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const useRegex = document.getElementById('useRegex').checked;
            const resultDiv = document.getElementById('findResult');

            if (!findText) {
                alert('请输入要查找的内容');
                return;
            }

            const text = originalText.value;
            let regex;

            try {
                if (useRegex) {
                    regex = new RegExp(findText, caseSensitive ? 'g' : 'gi');
                } else {
                    const escapedText = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    regex = new RegExp(escapedText, caseSensitive ? 'g' : 'gi');
                }

                findMatches = [...text.matchAll(regex)];

                if (findMatches.length === 0) {
                    resultDiv.textContent = '未找到匹配项';
                    return;
                }

                currentFindIndex = (currentFindIndex + 1) % findMatches.length;
                const match = findMatches[currentFindIndex];

                // 选中找到的文本
                originalText.focus();
                originalText.setSelectionRange(match.index, match.index + match[0].length);

                resultDiv.textContent = `找到 ${findMatches.length} 个匹配项，当前第 ${currentFindIndex + 1} 个`;

            } catch (error) {
                resultDiv.textContent = '正则表达式错误：' + error.message;
            }
        }

        function replaceAll() {
            const findText = document.getElementById('findText').value;
            const replaceText = document.getElementById('replaceText').value;
            const originalText = document.getElementById('originalText');
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const useRegex = document.getElementById('useRegex').checked;
            const resultDiv = document.getElementById('findResult');

            if (!findText) {
                alert('请输入要查找的内容');
                return;
            }

            let text = originalText.value;
            let regex;
            let replacedCount = 0;

            try {
                if (useRegex) {
                    regex = new RegExp(findText, caseSensitive ? 'g' : 'gi');
                } else {
                    const escapedText = findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    regex = new RegExp(escapedText, caseSensitive ? 'g' : 'gi');
                }

                const matches = text.match(regex);
                if (matches) {
                    replacedCount = matches.length;
                    text = text.replace(regex, replaceText);
                    originalText.value = text;
                    resultDiv.textContent = `已替换 ${replacedCount} 个匹配项`;

                    // 自动处理文本
                    setTimeout(() => {
                        processText();
                    }, 100);
                } else {
                    resultDiv.textContent = '未找到匹配项';
                }

            } catch (error) {
                resultDiv.textContent = '正则表达式错误：' + error.message;
            }
        }

        // 拆分文本功能 - 基于处理结果
        function splitText() {
            const processedDiv = document.getElementById('processedResult');

            // 检查是否有处理结果
            if (!processedDiv || !processedDiv.innerHTML.trim() || processedDiv.innerHTML.includes('处理结果将在这里显示')) {
                alert('请先处理文本后再进行拆分');
                return;
            }

            const pages = parseInt(document.getElementById('splitPages').value) || 2;
            if (pages < 2) {
                alert('至少要分成2页');
                return;
            }

            // 获取处理结果的HTML内容
            const processedHTML = processedDiv.innerHTML;

            // 按段落分割（处理结果中段落由<br><br>分隔）
            const paragraphs = processedHTML.split('<br><br>').filter(p => p.trim());

            if (paragraphs.length === 0) {
                alert('没有可拆分的内容');
                return;
            }

            // 计算每页的段落数
            const paragraphsPerPage = Math.ceil(paragraphs.length / pages);

            // 拆分成多页
            const splitPages = [];
            for (let i = 0; i < pages; i++) {
                const startIndex = i * paragraphsPerPage;
                const endIndex = Math.min(startIndex + paragraphsPerPage, paragraphs.length);
                const pageParagraphs = paragraphs.slice(startIndex, endIndex);

                if (pageParagraphs.length > 0) {
                    splitPages.push(pageParagraphs.join('<br><br>'));
                }
            }

            // 显示拆分结果
            const resultDiv = document.getElementById('splitResult');
            let html = '';

            splitPages.forEach((page, index) => {
                const pageNumber = index + 1;
                const wordCount = getTextLength(page);
                html += `
                    <div style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                        <div style="font-weight: bold; color: #007bff; margin-bottom: 4px;">第${pageNumber}页 (${wordCount}字符)</div>
                        <div style="max-height: 60px; overflow-y: auto; font-size: 12px; color: #666;">
                            ${getPlainText(page).substring(0, 100)}${getPlainText(page).length > 100 ? '...' : ''}
                        </div>
                    </div>
                `;
            });

            resultDiv.innerHTML = html;

            // 保存拆分结果
            window.splitResults = splitPages;

            // 更新复制按钮
            updateCopyButtons(pages);
        }

        // 获取纯文本长度
        function getTextLength(html) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            return (tempDiv.textContent || tempDiv.innerText || '').length;
        }

        // 获取纯文本内容
        function getPlainText(html) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            return tempDiv.textContent || tempDiv.innerText || '';
        }

        // 更新复制按钮
        function updateCopyButtons(pageCount) {
            const buttonContainer = document.getElementById('splitCopyButtons');
            let buttonsHTML = '';

            for (let i = 1; i <= pageCount; i++) {
                const buttonWidth = pageCount <= 3 ? 'flex: 1' : 'min-width: 80px';
                buttonsHTML += `<button class="btn" style="${buttonWidth}; font-size: 11px; margin-bottom: 4px;" onclick="copySplitPage(${i})">复制第${i}页</button>`;
            }

            buttonContainer.innerHTML = buttonsHTML;
        }

        // 复制指定页面
        function copySplitPage(pageNumber) {
            if (!window.splitResults || window.splitResults.length === 0) {
                alert('请先执行拆分操作');
                return;
            }

            if (pageNumber < 1 || pageNumber > window.splitResults.length) {
                alert('页面编号无效');
                return;
            }

            const pageHTML = window.splitResults[pageNumber - 1];

            // 转换为和处理结果一致的格式
            const formattedText = convertToProcessedFormat(pageHTML);

            // 复制到剪贴板
            navigator.clipboard.writeText(formattedText).then(() => {
                alert(`第${pageNumber}页已复制到剪贴板`);
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方案
                const textArea = document.createElement('textarea');
                textArea.value = formattedText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert(`第${pageNumber}页已复制到剪贴板`);
            });
        }

        // 转换为和处理结果一致的格式
        function convertToProcessedFormat(html) {
            // 创建临时div来处理HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // 移除所有高亮标签，保留文本
            const highlightElements = tempDiv.querySelectorAll('.highlight-symbol, .word-selected, .quick-highlight, .sentence-highlight');
            highlightElements.forEach(element => {
                const textNode = document.createTextNode(element.textContent);
                element.parentNode.replaceChild(textNode, element);
            });

            // 将<br><br>转换为双换行
            let text = tempDiv.innerHTML;
            text = text.replace(/<br><br>/g, '\n\n');
            text = text.replace(/<br>/g, '\n');

            // 移除其他HTML标签
            const cleanDiv = document.createElement('div');
            cleanDiv.innerHTML = text;
            text = cleanDiv.textContent || cleanDiv.innerText || '';

            return text;
        }

        // 自动拆分并显示第一页
        function autoSplitAndShow() {
            const processedDiv = document.getElementById('processedResult');

            // 检查是否有处理结果
            if (!processedDiv || !processedDiv.innerHTML.trim() || processedDiv.innerHTML.includes('处理结果将在这里显示')) {
                document.getElementById('currentPageContent').innerHTML = '<div style="text-align: center; color: #999; padding: 40px;">请先处理文本后再进行拆分</div>';
                return;
            }

            const pages = parseInt(document.getElementById('modalSplitPages').value) || 2;

            // 获取处理结果的HTML内容
            const processedHTML = processedDiv.innerHTML;

            // 按段落分割（处理结果中段落由<br><br>分隔）
            const paragraphs = processedHTML.split('<br><br>').filter(p => p.trim());

            if (paragraphs.length === 0) {
                document.getElementById('currentPageContent').innerHTML = '<div style="text-align: center; color: #999; padding: 40px;">没有可拆分的内容</div>';
                return;
            }

            // 计算每页的段落数
            const paragraphsPerPage = Math.ceil(paragraphs.length / pages);

            // 拆分成多页
            const splitPages = [];
            for (let i = 0; i < pages; i++) {
                const startIndex = i * paragraphsPerPage;
                const endIndex = Math.min(startIndex + paragraphsPerPage, paragraphs.length);
                const pageParagraphs = paragraphs.slice(startIndex, endIndex);

                if (pageParagraphs.length > 0) {
                    splitPages.push(pageParagraphs.join('<br><br>'));
                }
            }

            // 保存拆分结果
            window.modalSplitResults = splitPages;
            window.currentPageIndex = 0;

            // 显示第一页
            showCurrentPage();
        }

        // 显示当前页面
        function showCurrentPage() {
            if (!window.modalSplitResults || window.modalSplitResults.length === 0) {
                return;
            }

            const currentIndex = window.currentPageIndex || 0;
            const totalPages = window.modalSplitResults.length;
            const currentPageHTML = window.modalSplitResults[currentIndex];

            // 显示页面内容（保持HTML格式用于显示）
            document.getElementById('currentPageContent').innerHTML = currentPageHTML;

            // 更新页面指示器
            document.getElementById('pageIndicator').textContent = `第${currentIndex + 1}页 / 共${totalPages}页`;

            // 更新当前页字符数量
            const charCount = getTextLength(currentPageHTML);
            document.getElementById('currentPageCharCount').textContent = `${charCount}字符`;

            // 更新按钮状态
            document.getElementById('prevPageBtn').disabled = currentIndex === 0;
            document.getElementById('nextPageBtn').disabled = currentIndex === totalPages - 1;
        }

        // 显示上一页
        function showPreviousPage() {
            if (window.currentPageIndex > 0) {
                window.currentPageIndex--;
                showCurrentPage();
            }
        }

        // 显示下一页
        function showNextPage() {
            if (window.currentPageIndex < window.modalSplitResults.length - 1) {
                window.currentPageIndex++;
                showCurrentPage();
            }
        }

        // 复制当前页
        function copyCurrentPage() {
            if (!window.modalSplitResults || window.modalSplitResults.length === 0) {
                alert('没有可复制的内容');
                return;
            }

            const currentIndex = window.currentPageIndex || 0;
            const pageHTML = window.modalSplitResults[currentIndex];

            // 转换为和处理结果一致的格式
            const formattedText = convertToProcessedFormat(pageHTML);

            // 复制到剪贴板
            navigator.clipboard.writeText(formattedText).then(() => {
                alert(`第${currentIndex + 1}页已复制到剪贴板`);
            }).catch(err => {
                console.error('复制失败:', err);
                // 备用方案
                const textArea = document.createElement('textarea');
                textArea.value = formattedText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert(`第${currentIndex + 1}页已复制到剪贴板`);
            });
        }



        // 导出弹窗中的拆分结果
        function exportModalSplitResults() {
            if (!window.modalSplitResults || window.modalSplitResults.length === 0) {
                alert('请先执行拆分操作');
                return;
            }

            const zip = new JSZip();

            window.modalSplitResults.forEach((pageHTML, index) => {
                const pageNumber = index + 1;
                const filename = `第${pageNumber}页.txt`;
                const content = convertToProcessedFormat(pageHTML);
                zip.file(filename, content);
            });

            zip.generateAsync({type: "blob"}).then(function(content) {
                const url = URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = `拆分文本_${new Date().toISOString().slice(0, 10)}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                alert('拆分结果已导出为ZIP文件');
            });
        }

        function exportSplitResults() {
            if (!window.splitResults || window.splitResults.length === 0) {
                alert('请先执行拆分操作');
                return;
            }

            const zip = new JSZip();

            window.splitResults.forEach((pageHTML, index) => {
                const pageNumber = index + 1;
                const filename = `第${pageNumber}页.txt`;
                const content = convertToProcessedFormat(pageHTML);
                zip.file(filename, content);
            });

            zip.generateAsync({type: "blob"}).then(function(content) {
                const url = URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = `拆分文本_${new Date().toISOString().slice(0, 10)}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                alert('拆分结果已导出为ZIP文件');
            });
        }



        // 拆分文本功能
        function previewSplit() {
            const text = document.getElementById('originalText').value;
            const method = document.getElementById('splitMethod').value;
            const value = document.getElementById('splitValue').value;
            const infoDiv = document.getElementById('splitInfo');

            if (!text.trim()) {
                infoDiv.textContent = '请先输入文本内容';
                return;
            }

            if (!value.trim()) {
                infoDiv.textContent = '请输入拆分参数';
                return;
            }

            let parts = [];
            let info = '';

            try {
                switch (method) {
                    case 'lines':
                        const lineCount = parseInt(value);
                        if (isNaN(lineCount) || lineCount <= 0) {
                            infoDiv.textContent = '请输入有效的行数';
                            return;
                        }
                        const lines = text.split('\n');
                        for (let i = 0; i < lines.length; i += lineCount) {
                            parts.push(lines.slice(i, i + lineCount).join('\n'));
                        }
                        info = `将拆分为 ${parts.length} 个文件，每个文件最多 ${lineCount} 行`;
                        break;

                    case 'chars':
                        const charCount = parseInt(value);
                        if (isNaN(charCount) || charCount <= 0) {
                            infoDiv.textContent = '请输入有效的字符数';
                            return;
                        }
                        for (let i = 0; i < text.length; i += charCount) {
                            parts.push(text.slice(i, i + charCount));
                        }
                        info = `将拆分为 ${parts.length} 个文件，每个文件最多 ${charCount} 个字符`;
                        break;

                    case 'words':
                        const wordCount = parseInt(value);
                        if (isNaN(wordCount) || wordCount <= 0) {
                            infoDiv.textContent = '请输入有效的词数';
                            return;
                        }
                        const words = text.split(/\s+/);
                        for (let i = 0; i < words.length; i += wordCount) {
                            parts.push(words.slice(i, i + wordCount).join(' '));
                        }
                        info = `将拆分为 ${parts.length} 个文件，每个文件最多 ${wordCount} 个词`;
                        break;

                    case 'custom':
                        if (!value) {
                            infoDiv.textContent = '请输入分隔符';
                            return;
                        }
                        parts = text.split(value);
                        info = `按分隔符 "${value}" 拆分为 ${parts.length} 个文件`;
                        break;
                }

                infoDiv.innerHTML = `
                    <div style="color: #28a745; font-weight: 500;">${info}</div>
                    <div style="margin-top: 8px; font-size: 12px; color: #666;">
                        文件大小范围：${Math.min(...parts.map(p => p.length))} - ${Math.max(...parts.map(p => p.length))} 字符
                    </div>
                `;

            } catch (error) {
                infoDiv.textContent = '拆分参数错误：' + error.message;
            }
        }

        function executeSplit() {
            const text = document.getElementById('originalText').value;
            const method = document.getElementById('splitMethod').value;
            const value = document.getElementById('splitValue').value;

            if (!text.trim()) {
                alert('请先输入文本内容');
                return;
            }

            let parts = [];

            try {
                switch (method) {
                    case 'lines':
                        const lineCount = parseInt(value);
                        const lines = text.split('\n');
                        for (let i = 0; i < lines.length; i += lineCount) {
                            parts.push(lines.slice(i, i + lineCount).join('\n'));
                        }
                        break;

                    case 'chars':
                        const charCount = parseInt(value);
                        for (let i = 0; i < text.length; i += charCount) {
                            parts.push(text.slice(i, i + charCount));
                        }
                        break;

                    case 'words':
                        const wordCount = parseInt(value);
                        const words = text.split(/\s+/);
                        for (let i = 0; i < words.length; i += wordCount) {
                            parts.push(words.slice(i, i + wordCount).join(' '));
                        }
                        break;

                    case 'custom':
                        parts = text.split(value);
                        break;
                }

                // 创建ZIP文件
                const zip = new JSZip();
                const timestamp = new Date().toISOString().slice(0, 10);

                parts.forEach((part, index) => {
                    const filename = `拆分文本_${timestamp}_${String(index + 1).padStart(3, '0')}.txt`;
                    zip.file(filename, part);
                });

                zip.generateAsync({type: 'blob'}).then(function(content) {
                    const url = URL.createObjectURL(content);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `拆分文本_${timestamp}.zip`;
                    a.click();
                    URL.revokeObjectURL(url);

                    closeModal('splitTextModal');
                    alert(`成功拆分为 ${parts.length} 个文件并下载`);
                });

            } catch (error) {
                alert('拆分失败：' + error.message);
            }
        }

        // 敏感词管理功能
        function addSensitiveWord() {
            const word = document.getElementById('newSensitiveWord').value.trim();
            const severity = document.getElementById('newWordSeverity').value;

            if (!word) {
                alert('请输入敏感词');
                return;
            }

            sensitiveProcessor.addWord(word, 'custom', severity);
            document.getElementById('newSensitiveWord').value = '';
            updateSensitiveWordsList();

            // 清除快速检查缓存，确保新敏感词能被检测到
            lastCheckText = '';
            lastCheckResults = null;
            lastSensitiveWordsHash = '';
        }

        // 弹窗中的敏感词添加功能
        // 搜索敏感词功能
        function searchSensitiveWords() {
            const searchInput = document.getElementById('searchSensitiveWord');
            const searchTerm = searchInput.value.trim().toLowerCase();

            updateModalSensitiveWordsList(searchTerm);
        }

        // 清除搜索
        function clearSensitiveSearch() {
            const searchInput = document.getElementById('searchSensitiveWord');
            searchInput.value = '';
            updateModalSensitiveWordsList();
        }

        // 弹窗中添加敏感词功能
        function addModalSensitiveWord() {
            const wordInput = document.getElementById('modalNewSensitiveWord');
            const replacementInput = document.getElementById('modalNewReplacement');

            const word = wordInput.value.trim();
            let replacement = replacementInput.value.trim();

            if (!word) {
                alert('请输入敏感词');
                wordInput.focus();
                return;
            }

            // 检查敏感词是否已存在
            if (sensitiveProcessor.sensitiveWords.has(word)) {
                alert('该敏感词已存在');
                wordInput.focus();
                return;
            }

            // 如果没有输入替换词，设置为特殊标记表示只高亮
            if (!replacement) {
                replacement = '__HIGHLIGHT_ONLY__';
            }

            // 添加敏感词
            sensitiveProcessor.addCustomWord(word, replacement);

            // 清空输入框
            wordInput.value = '';
            replacementInput.value = '';

            // 刷新列表
            updateModalSensitiveWordsList();
            updateSensitiveWordsList(); // 同时更新主界面的列表

            // 清除快速检查缓存，确保新敏感词能被检测到
            lastCheckText = '';
            lastCheckResults = null;
            lastSensitiveWordsHash = '';

            const action = replacement === '__HIGHLIGHT_ONLY__' ? '(仅高亮)' : `(替换为: ${replacement})`;
            console.log(`敏感词添加成功: ${word} ${action}`);

            // 给用户反馈
            wordInput.style.backgroundColor = '#d4edda';
            setTimeout(() => {
                wordInput.style.backgroundColor = '';
                wordInput.focus(); // 重新聚焦，方便连续添加
            }, 500);
        }

        function updateSensitiveWordsList() {
            const listDiv = document.getElementById('sensitiveWordsList');
            listDiv.innerHTML = '';

            const allWords = sensitiveProcessor.getAllWords();

            if (allWords.length === 0) {
                listDiv.innerHTML = `
                    <div style="padding: 20px; text-align: center; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px; border: 1px dashed #dee2e6; margin: 10px 0;">
                        <div style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;">🔍</div>
                        <div style="font-size: 12px; color: #6c757d;">暂无敏感词</div>
                    </div>
                `;
                return;
            }

            // 按照先有替换词后有只高亮的顺序排列，同类型内按字母排序
            const sortedWords = allWords.sort((a, b) => {
                const aIsHighlightOnly = a.replacement === '__HIGHLIGHT_ONLY__';
                const bIsHighlightOnly = b.replacement === '__HIGHLIGHT_ONLY__';

                // 如果一个有替换词，一个只高亮，有替换词的排在前面
                if (!aIsHighlightOnly && bIsHighlightOnly) return -1;
                if (aIsHighlightOnly && !bIsHighlightOnly) return 1;

                // 同类型的按字母排序
                return a.word.localeCompare(b.word, 'zh-CN');
            });

            sortedWords.forEach(word => {
                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = `
                    padding: 8px 12px;
                    margin-bottom: 6px;
                    border-radius: 6px;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    border: 1px solid #dee2e6;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    transition: all 0.2s ease;
                    position: relative;
                `;

                // 添加悬停效果
                itemDiv.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-1px)';
                    this.style.boxShadow = '0 2px 6px rgba(0,0,0,0.15)';
                });

                itemDiv.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                });

                const isHighlightOnly = word.replacement === '__HIGHLIGHT_ONLY__';
                const replacementText = isHighlightOnly ? '仅高亮' : word.replacement;
                const actionColor = isHighlightOnly ? '#17a2b8' : '#28a745';

                itemDiv.innerHTML = `
                    <div style="display: grid; grid-template-columns: 1fr 1fr 40px; gap: 8px; align-items: center; width: 100%;">
                        <div>
                            <input type="text" value="${word.word}"
                                   onblur="updateWordText('${word.word}', this.value, '${word.replacement}'); updateSensitiveWordsList();"
                                   onkeypress="if(event.key==='Enter') this.blur()"
                                   style="width: 100%; padding: 6px 8px; border: 2px solid #e9ecef; border-radius: 6px; font-size: 12px; font-weight: 500; transition: all 0.2s ease; background: white;"
                                   onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 2px rgba(0,123,255,0.1)'"
                                   onblur="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'; updateWordText('${word.word}', this.value, '${word.replacement}'); updateSensitiveWordsList();">
                        </div>
                        <div>
                            <input type="text" value="${isHighlightOnly ? '' : word.replacement}"
                                   placeholder="留空仅高亮"
                                   onblur="updateWordReplacement('${word.word}', this.value); updateSensitiveWordsList();"
                                   onkeypress="if(event.key==='Enter') this.blur()"
                                   style="width: 100%; padding: 6px 8px; border: 2px solid #e9ecef; border-radius: 6px; font-size: 12px; transition: all 0.2s ease; background: white;"
                                   onfocus="this.style.borderColor='#28a745'; this.style.boxShadow='0 0 0 2px rgba(40,167,69,0.1)'"
                                   onblur="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'; updateWordReplacement('${word.word}', this.value); updateSensitiveWordsList();">
                        </div>
                        <div>
                            <button onclick="removeSensitiveWord('${word.word}')"
                                    style="width: 100%; background: linear-gradient(135deg, #dc3545, #c82333); color: white; border: none; padding: 6px 4px; border-radius: 6px; cursor: pointer; font-size: 10px; font-weight: 500; transition: all 0.2s ease;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">🗑️</button>
                        </div>
                    </div>
                    <div style="margin-top: 6px; font-size: 10px; color: #6c757d; display: flex; justify-content: space-between; align-items: center;">
                        <span>${word.category}</span>
                        <span style="color: ${actionColor}; font-weight: 500;">${isHighlightOnly ? '🔍 仅高亮' : '🔄 替换'}</span>
                    </div>
                `;

                listDiv.appendChild(itemDiv);
            });
        }

        // 优化的弹窗敏感词列表更新函数
        function updateModalSensitiveWordsList(searchTerm = '') {
            const listDiv = document.getElementById('modalSensitiveWordsList');

            // 直接处理数据，不显示加载状态
            try {
                console.log('🔍 开始获取敏感词列表...');
                const allWords = sensitiveProcessor.getAllWords();
                console.log('📋 获取到敏感词:', allWords.length, '个');

            // 如果有搜索词，进行过滤
            let filteredWords = allWords;
            if (searchTerm) {
                filteredWords = allWords.filter(word =>
                    word.word.toLowerCase().includes(searchTerm) ||
                    (word.replacement && word.replacement.toLowerCase().includes(searchTerm)) ||
                    (word.category && word.category.toLowerCase().includes(searchTerm))
                );
            }

            if (filteredWords.length === 0) {
                const message = searchTerm ?
                    `未找到包含"${searchTerm}"的敏感词` :
                    '暂无敏感词';
                const subMessage = searchTerm ?
                    '尝试使用其他关键词搜索' :
                    '点击上方"搜索"功能来查找敏感词';

                listDiv.innerHTML = `
                    <div style="padding: 20px; text-align: center; background: #f8f9fa; border-radius: 6px; border: 1px dashed #dee2e6;">
                        <div style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;">${searchTerm ? '🔍' : '📝'}</div>
                        <div style="font-size: 13px; color: #6c757d; margin-bottom: 4px;">${message}</div>
                        <div style="font-size: 11px; color: #adb5bd;">${subMessage}</div>
                    </div>
                `;
                return;
            }

            // 按照先有替换词后有只高亮的顺序排列，同类型内按字母排序
            const sortedWords = filteredWords.sort((a, b) => {
                const aIsHighlightOnly = a.replacement === '__HIGHLIGHT_ONLY__';
                const bIsHighlightOnly = b.replacement === '__HIGHLIGHT_ONLY__';

                // 如果一个有替换词，一个只高亮，有替换词的排在前面
                if (!aIsHighlightOnly && bIsHighlightOnly) return -1;
                if (aIsHighlightOnly && !bIsHighlightOnly) return 1;

                // 同类型的按字母排序
                return a.word.localeCompare(b.word, 'zh-CN');
            });

            // 如果是搜索结果，显示搜索信息
            if (searchTerm) {
                const searchInfo = document.createElement('div');
                searchInfo.style.cssText = 'padding: 6px 8px; background: #e3f2fd; border-radius: 4px; margin-bottom: 6px; font-size: 11px; color: #1976d2;';
                searchInfo.innerHTML = `🔍 找到 ${filteredWords.length} 个结果`;
                listDiv.appendChild(searchInfo);
            }

            sortedWords.forEach((word, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = `
                    padding: 8px 12px;
                    margin-bottom: 6px;
                    border-radius: 6px;
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    transition: all 0.2s ease;
                `;

                // 添加悬停效果
                itemDiv.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#e9ecef';
                    this.style.borderColor = '#adb5bd';
                });

                itemDiv.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '#f8f9fa';
                    this.style.borderColor = '#dee2e6';
                });

                const isHighlightOnly = word.replacement === '__HIGHLIGHT_ONLY__';
                const replacementText = isHighlightOnly ? '仅高亮' : word.replacement;
                const actionColor = isHighlightOnly ? '#17a2b8' : '#28a745';

                // 高亮搜索词
                function highlightSearchTerm(text, searchTerm) {
                    if (!searchTerm) return text;
                    const regex = new RegExp(`(${searchTerm})`, 'gi');
                    return text.replace(regex, '<span style="background: #ffeb3b; padding: 1px 2px; border-radius: 2px;">$1</span>');
                }

                const highlightedWord = highlightSearchTerm(word.word, searchTerm);
                const highlightedReplacement = highlightSearchTerm(word.replacement || '', searchTerm);
                const highlightedCategory = highlightSearchTerm(word.category || '', searchTerm);

                const contentDiv = document.createElement('div');

                contentDiv.innerHTML = `
                    <!-- 编辑区域 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr 50px; gap: 8px; align-items: center;">
                        <div>
                            <input type="text" value="${word.word}"
                                   onblur="updateWordText('${word.word}', this.value, '${word.replacement}')"
                                   onkeypress="if(event.key==='Enter') this.blur()"
                                   style="width: 100%; padding: 4px 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; background: white;"
                                   title="敏感词">
                        </div>
                        <div>
                            <input type="text" value="${isHighlightOnly ? '' : word.replacement}"
                                   placeholder="替换词(可选)"
                                   onblur="updateWordReplacement('${word.word}', this.value)"
                                   onkeypress="if(event.key==='Enter') this.blur()"
                                   style="width: 100%; padding: 4px 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; background: white;"
                                   title="替换词">
                        </div>
                        <div style="text-align: center;">
                            <button onclick="removeModalSensitiveWord('${word.word}')"
                                    style="width: 100%; background: #dc3545; color: white; border: none; padding: 4px 6px; border-radius: 4px; cursor: pointer; font-size: 11px; transition: all 0.2s ease;"
                                    onmouseover="this.style.backgroundColor='#c82333'"
                                    onmouseout="this.style.backgroundColor='#dc3545'"
                                    title="删除敏感词">
                                🗑️
                            </button>
                        </div>
                    </div>
                `;

                itemDiv.appendChild(contentDiv);

                listDiv.appendChild(itemDiv);
            });
                } catch (error) {
                    console.error('❌ 加载敏感词列表时出错:', error);
                    listDiv.innerHTML = `
                        <div style="text-align: center; padding: 30px; color: #dc3545;">
                            <div style="font-size: 48px; margin-bottom: 10px;">⚠️</div>
                            <div style="font-size: 16px; margin-bottom: 8px;">加载失败</div>
                            <div style="font-size: 14px; color: #6c757d;">请刷新页面重试</div>
                        </div>
                    `;
                }
        }

        function removeSensitiveWord(word) {
            if (confirm(`确定要删除敏感词 "${word}" 吗？`)) {
                sensitiveProcessor.removeWord(word);
                updateSensitiveWordsList();

                // 清除快速检查缓存，确保删除的敏感词不再被检测到
                lastCheckText = '';
                lastCheckResults = null;
                lastSensitiveWordsHash = '';
            }
        }

        function removeModalSensitiveWord(word) {
            if (confirm(`确定要删除敏感词 "${word}" 吗？`)) {
                sensitiveProcessor.removeWord(word);
                updateModalSensitiveWordsList();
            }
        }



        // 更新敏感词文本
        function updateWordText(oldWord, newWord, currentReplacement) {
            newWord = newWord.trim();

            if (!newWord) {
                alert('敏感词不能为空');
                updateModalSensitiveWordsList(); // 恢复原值
                return;
            }

            if (newWord === oldWord) {
                return; // 没有变化
            }

            // 检查新词是否已存在
            if (sensitiveProcessor.sensitiveWords.has(newWord)) {
                alert('该敏感词已存在');
                updateModalSensitiveWordsList(); // 恢复原值
                return;
            }

            // 获取原词信息
            const wordInfo = sensitiveProcessor.sensitiveWords.get(oldWord);
            if (wordInfo) {
                // 删除原词
                sensitiveProcessor.sensitiveWords.delete(oldWord);

                // 添加新词，保持其他属性不变
                sensitiveProcessor.sensitiveWords.set(newWord, {
                    ...wordInfo,
                    word: newWord
                });

                // 刷新列表
                updateModalSensitiveWordsList();
                console.log(`敏感词已从 "${oldWord}" 更新为 "${newWord}"`);
            }
        }

        // 更新替换词
        function updateWordReplacement(word, newReplacement) {
            newReplacement = newReplacement.trim();

            // 获取当前敏感词信息
            const wordInfo = sensitiveProcessor.sensitiveWords.get(word);
            if (wordInfo) {
                // 如果替换词为空，设置为只高亮模式
                const replacement = newReplacement || '__HIGHLIGHT_ONLY__';

                // 更新替换词
                wordInfo.replacement = replacement;
                sensitiveProcessor.sensitiveWords.set(word, wordInfo);

                // 刷新列表显示
                updateModalSensitiveWordsList();

                // 显示成功提示
                const mode = replacement === '__HIGHLIGHT_ONLY__' ? '仅高亮' : `替换为"${replacement}"`;
                console.log(`敏感词 "${word}" 已设置为：${mode}`);
            }
        }

        function importSensitiveWords() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.txt,.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const content = e.target.result;
                        let words = [];

                        if (file.name.endsWith('.json')) {
                            words = JSON.parse(content);
                        } else {
                            words = content.split('\n').map(line => line.trim()).filter(line => line);
                            words = words.map(word => ({ word, category: 'imported', severity: 'medium' }));
                        }

                        let importedCount = 0;
                        words.forEach(wordObj => {
                            if (typeof wordObj === 'string') {
                                // 导入的纯文本敏感词默认设置为"仅高亮"
                                sensitiveProcessor.addCustomWord(wordObj, '__HIGHLIGHT_ONLY__', 'imported', 'medium');
                                importedCount++;
                            } else {
                                // JSON格式的敏感词，保持原有的替换设置
                                const replacement = wordObj.replacement || '__HIGHLIGHT_ONLY__';
                                sensitiveProcessor.addCustomWord(wordObj.word, replacement, wordObj.category || 'imported', wordObj.severity || 'medium');
                                importedCount++;
                            }
                        });

                        // 更新列表（会自动按先替换后高亮排序）
                        updateSensitiveWordsList();
                        updateModalSensitiveWordsList();

                        alert(`✅ 成功导入 ${importedCount} 个敏感词\n📋 已按"先替换后高亮"顺序排列`);

                    } catch (error) {
                        alert('导入失败：' + error.message);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        }

        // 手动保存敏感词（双重保障）
        function manualSaveSensitiveWords() {
            try {
                // 执行保存操作
                const success = sensitiveProcessor.saveToStorage();

                if (success) {
                    // 获取当前敏感词数量
                    const wordCount = sensitiveProcessor.getAllWords().length;

                    // 显示成功消息
                    alert(`✅ 敏感词数据保存成功！\n\n📊 共保存 ${wordCount} 个敏感词\n💾 数据已安全存储到本地`);

                    // 清除快速检查缓存，确保保存后的数据生效
                    lastCheckText = '';
                    lastCheckResults = null;
                    lastSensitiveWordsHash = '';

                    console.log('🔒 手动保存敏感词成功，缓存已清除');
                } else {
                    alert('❌ 保存失败，请检查浏览器存储权限');
                }
            } catch (error) {
                console.error('❌ 手动保存敏感词出错:', error);
                alert('❌ 保存过程中发生错误，请重试');
            }
        }

        function exportSensitiveWords() {
            const words = sensitiveProcessor.getAllWords();

            if (words.length === 0) {
                alert('没有敏感词可导出');
                return;
            }

            // 按分类和严重程度分组
            const categories = {
                'political': '政治敏感',
                'violence': '暴力内容',
                'adult': '成人内容',
                'drugs': '毒品相关',
                'gambling': '赌博相关',
                'relationship': '情感关系',
                'vulgar': '粗俗用语',
                'custom': '自定义词汇',
                'imported': '导入词汇'
            };

            const severityNames = {
                'high': '高危',
                'medium': '中危',
                'low': '低危'
            };

            // 生成Word文档内容
            let htmlContent = '';

            // 按分类分组
            const groupedWords = {};
            words.forEach(word => {
                if (!groupedWords[word.category]) {
                    groupedWords[word.category] = [];
                }
                groupedWords[word.category].push(word);
            });

            // 生成HTML内容
            htmlContent += '<h1 style="text-align: center; color: #333; margin-bottom: 30px;">敏感词库导出</h1>';
            htmlContent += `<p style="text-align: center; color: #666; margin-bottom: 30px;">导出时间：${new Date().toLocaleString()}</p>`;
            htmlContent += `<p style="text-align: center; color: #666; margin-bottom: 40px;">总计：${words.length} 个敏感词</p>`;

            // 按分类输出
            Object.keys(groupedWords).forEach(category => {
                const categoryWords = groupedWords[category];
                const categoryName = categories[category] || category;

                htmlContent += `<h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; margin-top: 30px;">${categoryName} (${categoryWords.length}个)</h2>`;

                // 按严重程度分组
                const severityGroups = {
                    'high': [],
                    'medium': [],
                    'low': []
                };

                categoryWords.forEach(word => {
                    severityGroups[word.severity].push(word);
                });

                // 输出每个严重程度的词汇
                Object.keys(severityGroups).forEach(severity => {
                    const severityWords = severityGroups[severity];
                    if (severityWords.length > 0) {
                        const severityColor = severity === 'high' ? '#e74c3c' :
                                            severity === 'medium' ? '#f39c12' : '#27ae60';

                        htmlContent += `<h3 style="color: ${severityColor}; margin-top: 20px; margin-bottom: 10px;">${severityNames[severity]} (${severityWords.length}个)</h3>`;
                        htmlContent += '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
                        htmlContent += '<tr style="background: #f8f9fa;"><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">敏感词</th><th style="border: 1px solid #ddd; padding: 8px; text-align: left;">替换词</th></tr>';

                        severityWords.forEach(word => {
                            htmlContent += `<tr>`;
                            htmlContent += `<td style="border: 1px solid #ddd; padding: 8px;">${word.word}</td>`;
                            htmlContent += `<td style="border: 1px solid #ddd; padding: 8px;">${word.replacement}</td>`;
                            htmlContent += `</tr>`;
                        });

                        htmlContent += '</table>';
                    }
                });
            });

            // 生成完整的Word文档HTML
            const wordDocument = `<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>100</w:Zoom>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 0;
            padding: 2cm;
        }
        h1 {
            font-size: 18pt;
            margin: 0 0 20pt 0;
        }
        h2 {
            font-size: 14pt;
            margin: 20pt 0 10pt 0;
        }
        h3 {
            font-size: 12pt;
            margin: 15pt 0 8pt 0;
        }
        p {
            margin: 6pt 0;
        }
        table {
            font-size: 11pt;
        }
        th, td {
            font-size: 11pt;
        }
    </style>
</head>
<body>${htmlContent}</body>
</html>`;

            // 创建并下载Word文档
            const blob = new Blob([wordDocument], {
                type: 'application/msword;charset=utf-8'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `敏感词库_${new Date().toISOString().slice(0, 10)}.doc`;
            a.click();
            URL.revokeObjectURL(url);

            alert(`敏感词库已导出为Word文档！\n总计：${words.length} 个敏感词`);
        }

        // 导出Word文档功能
        function previewExport() {
            const fileName = document.getElementById('fileName').value || '文本处理结果';
            const preserveFormat = document.getElementById('preserveFormat').checked;
            const previewDiv = document.getElementById('previewInfo');

            const processedResultDiv = document.getElementById('processedResult');
            const processedText = processedResultDiv.textContent || processedResultDiv.innerText;

            const contentLength = processedText.length;
            const finalFileName = fileName + '.doc';

            previewDiv.innerHTML = `
                <div style="color: #28a745; font-weight: 500;">导出配置预览</div>
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    <div>文件名：${finalFileName}</div>
                    <div>内容类型：处理结果</div>
                    <div>内容长度：${contentLength} 字符</div>
                    <div>保持格式：${preserveFormat ? '是' : '否'}</div>
                </div>
            `;
        }

        async function exportToWord() {
            // 直接调用纯净版的逻辑，然后添加高亮
            const fileName = document.getElementById('fileName').value || '文本处理结果';

            const processedResultDiv = document.getElementById('processedResult');
            if (!processedResultDiv) {
                alert('找不到处理结果区域');
                return;
            }

            const originalHTML = processedResultDiv.innerHTML;

            // 使用与纯净版完全相同的处理逻辑
            let processedHTML = originalHTML;
            processedHTML = processedHTML.replace(/<br><br>/gi, '\n');
            processedHTML = processedHTML.replace(/<br\s*\/?>/gi, '\n');

            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = processedHTML;
            const pureTextContent = tempDiv.textContent || tempDiv.innerText || '';

            if (!pureTextContent.trim() || pureTextContent === '处理结果将在这里显示...') {
                alert('请先处理文本再导出');
                return;
            }

            // 使用与纯净版完全相同的分割逻辑
            let paragraphs;
            let doubleSplit = pureTextContent.split('\n\n').filter(p => p.trim());

            if (doubleSplit.length > 1) {
                paragraphs = doubleSplit;
            } else {
                let singleSplit = pureTextContent.split('\n').filter(p => p.trim());
                if (singleSplit.length > 1) {
                    paragraphs = singleSplit;
                } else {
                    paragraphs = [pureTextContent.trim()];
                }
            }

            // 生成HTML，在纯净版基础上添加高亮
            let pureText = '';
            for (let i = 0; i < paragraphs.length; i++) {
                const paragraph = paragraphs[i].trim();
                if (paragraph) {
                    // 添加高亮标记
                    let highlightedParagraph = paragraph;
                    highlightedParagraph = highlightedParagraph.replace(/？/g, '<span style="background:#ffff00;color:#ff0000;font-weight:bold;">？</span>');
                    highlightedParagraph = highlightedParagraph.replace(/！/g, '<span style="background:#ffff00;color:#ff0000;font-weight:bold;">！</span>');
                    highlightedParagraph = highlightedParagraph.replace(/：/g, '<span style="background:#ffff00;color:#ff0000;font-weight:bold;">：</span>');
                    highlightedParagraph = highlightedParagraph.replace(/…/g, '<span style="background:#ffff00;color:#ff0000;font-weight:bold;">…</span>');

                    pureText += `<p>${highlightedParagraph}</p>`;
                }
            }

            // 确保内容开头没有空白
            pureText = pureText.trim();

            // 使用与纯净版完全相同的HTML文档结构
            const htmlContent = `<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>100</w:Zoom>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.8;
            margin: 0;
            padding: 2cm;
        }
        p {
            margin: 0 0 6pt 0;
            padding: 0;
            text-indent: 2em;
            mso-pagination: widow-orphan;
        }
        p:first-child {
            margin-top: 0;
        }
    </style>
</head>
<body>${pureText}</body>
</html>`;

            try {
                // 创建Blob并下载，使用与纯净版相同的MIME类型
                const blob = new Blob([htmlContent], {
                    type: 'application/msword;charset=utf-8'
                });

                const finalFileName = fileName + '.doc';
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = finalFileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                closeModal('exportWordModal');
                alert(`带高亮Word文档 "${finalFileName}" 导出成功！`);

            } catch (error) {
                console.error('导出Word文档失败:', error);
                alert('导出失败：' + error.message);
            }
        }

        // 导出并关闭弹窗的函数，防止重复导出
        let exportInProgress = false;
        async function exportToWordAndClose() {
            // 防止重复导出
            if (exportInProgress) {
                console.log('导出正在进行中，忽略重复请求');
                return;
            }

            exportInProgress = true;
            try {
                await exportToWord();
                // exportToWord函数内部已经关闭了弹窗
            } catch (error) {
                console.error('导出失败:', error);
                // 如果导出失败，确保弹窗还是关闭
                closeModal('exportWordModal');
            } finally {
                exportInProgress = false;
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }



        // 自动生成文件名
        function generateFileName() {
            return '文本处理结果';
        }

        // 当打开导出弹窗时自动生成文件名并全选
        function openExportWordModal() {
            const fileNameInput = document.getElementById('fileName');
            fileNameInput.value = generateFileName();
            openModal('exportWordModal');

            // 延迟一点时间确保弹窗完全显示后再全选
            setTimeout(() => {
                fileNameInput.focus();
                fileNameInput.select();

                // 添加回车键监听，防止重复触发
                const modal = document.getElementById('exportWordModal');
                let isExporting = false; // 防止重复导出的标志

                const handleKeyPress = (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        e.stopPropagation();

                        // 防止重复导出
                        if (isExporting) return;
                        isExporting = true;

                        // 先移除事件监听器
                        modal.removeEventListener('keydown', handleKeyPress);
                        // 执行导出并关闭弹窗
                        exportToWordAndClose().finally(() => {
                            isExporting = false;
                        });
                    }
                    if (e.key === 'Escape') {
                        closeModal('exportWordModal');
                        modal.removeEventListener('keydown', handleKeyPress);
                    }
                };
                modal.addEventListener('keydown', handleKeyPress);
                modal.focus();
            }, 100);
        }

        // 直接生成纯Word文档功能
        function generatePureWordDoc() {
            // 从处理结果区域获取HTML内容
            const processedResultDiv = document.getElementById('processedResult');

            if (!processedResultDiv) {
                alert('找不到处理结果区域');
                return;
            }

            // 获取原始HTML内容
            const originalHTML = processedResultDiv.innerHTML;

            // 使用更可靠的方法提取文本
            // 先替换<br><br>为换行符，然后移除HTML标签
            let processedHTML = originalHTML;
            processedHTML = processedHTML.replace(/<br><br>/gi, '\n');
            processedHTML = processedHTML.replace(/<br\s*\/?>/gi, '\n');

            // 创建临时div来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = processedHTML;
            const pureTextContent = tempDiv.textContent || tempDiv.innerText || '';

            if (!pureTextContent.trim() || pureTextContent === '处理结果将在这里显示...') {
                alert('请先处理文本再导出');
                return;
            }

            // 修复：使用正确的元素ID
            const filename = document.getElementById('fileName').value || '处理结果';

            // 更智能的段落分割，确保不丢失任何内容
            let paragraphs;

            // 首先尝试按双换行分割
            let doubleSplit = pureTextContent.split('\n\n').filter(p => p.trim());

            if (doubleSplit.length > 1) {
                // 如果有多个段落，使用双换行分割的结果
                paragraphs = doubleSplit;
            } else {
                // 如果只有一个段落，尝试按单换行分割
                let singleSplit = pureTextContent.split('\n').filter(p => p.trim());

                if (singleSplit.length > 1) {
                    paragraphs = singleSplit;
                } else {
                    // 如果还是只有一段，直接使用整个文本
                    paragraphs = [pureTextContent.trim()];
                }
            }

            // 修复Word第一段丢失问题：使用更精确的方法
            let pureText = '';

            // 生成HTML段落，确保每一段都被处理，使用最简洁的格式
            for (let i = 0; i < paragraphs.length; i++) {
                const paragraph = paragraphs[i].trim();
                if (paragraph) {
                    // 使用最简单的段落格式，避免任何可能造成偏差的属性
                    pureText += `<p>${paragraph}</p>`;
                }
            }

            if (!pureText) {
                pureText = '<p>处理后没有有效内容</p>';
            }

            // 确保内容开头没有空白
            pureText = pureText.trim();

            // 修复Word兼容性：添加空段落和特殊标记确保第一段不被丢失
            const htmlContent = `<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>100</w:Zoom>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.8;
            margin: 0;
            padding: 2cm;
        }
        p {
            margin: 0 0 6pt 0;
            padding: 0;
            text-indent: 2em;
            mso-pagination: widow-orphan;
        }
        p:first-child {
            margin-top: 0;
        }
    </style>
</head>
<body>${pureText}</body>
</html>`;

            // 修复后重新使用HTML格式导出

            // 创建HTML文件并下载
            const blob = new Blob([htmlContent], {
                type: 'application/msword;charset=utf-8'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename + '.doc';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            closeModal('exportWordModal');
            alert(`纯净Word文档 "${filename}.doc" 导出成功！`);
        }

        function generatePureTextContent(htmlText) {
            // 使用最可靠的方法确保第一行不丢失
            if (!htmlText || !htmlText.trim()) {
                return '<p>没有内容可导出</p>';
            }

            // 创建一个临时div来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlText;

            // 获取纯文本内容，这样可以保证所有内容都被保留
            const pureText = tempDiv.textContent || tempDiv.innerText || '';

            // 如果没有内容，返回提示
            if (!pureText.trim()) {
                return '<p>处理后没有有效内容</p>';
            }

            // 按段落分割（处理结果中的段落由双换行分隔）
            const paragraphs = pureText.split(/\n\s*\n/).filter(p => p.trim());

            // 如果没有找到段落分隔，就按单换行分割
            let finalParagraphs;
            if (paragraphs.length <= 1) {
                finalParagraphs = pureText.split('\n').filter(p => p.trim());
            } else {
                finalParagraphs = paragraphs;
            }

            // 生成HTML段落
            let pureContent = '';
            for (let i = 0; i < finalParagraphs.length; i++) {
                const paragraph = finalParagraphs[i].trim();
                if (paragraph) {
                    pureContent += `<p>${paragraph}</p>\n`;
                }
            }

            return pureContent || '<p>处理后没有有效内容</p>';
        }


    </script>



    <!-- 查找替换弹窗 -->
    <div id="findReplaceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">🔍 查找替换</h3>
                <button class="modal-close" onclick="closeModal('findReplaceModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">查找内容：</label>
                        <input type="text" class="function-input" id="findText" placeholder="输入要查找的内容" style="width: 100%;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">替换为：</label>
                        <input type="text" class="function-input" id="replaceText" placeholder="输入替换后的内容" style="width: 100%;">
                    </div>
                    <div style="display: flex; gap: 20px; margin: 10px 0;">
                        <label style="display: flex; align-items: center; gap: 5px; font-size: 14px;">
                            <input type="checkbox" id="caseSensitive"> 区分大小写
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px; font-size: 14px;">
                            <input type="checkbox" id="useRegex"> 正则表达式
                        </label>
                    </div>
                    <div id="findResult" style="font-size: 13px; color: #666; margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 6px; min-height: 20px;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-info">
                    💡 支持正则表达式和大小写敏感查找
                </div>
                <div class="modal-footer-buttons">
                    <button class="btn" onclick="findNext()">🔍 查找下一个</button>
                    <button class="btn success" onclick="replaceAll()">🔄 全部替换</button>
                    <button class="btn" onclick="closeModal('findReplaceModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速检查弹窗 -->
    <div id="quickCheckModal" class="modal">
        <div class="modal-content" style="width: 420px; height: 500px; position: fixed; left: calc(50% - 420px); top: 42px; border-radius: 6px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); border: 1px solid #ccc;">
            <div class="modal-header" style="background: #f0f0f0; border-bottom: 1px solid #ddd; padding: 8px 12px; border-radius: 6px 6px 0 0; display: flex; align-items: center; justify-content: space-between; height: 32px;">
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="font-size: 14px; color: #0078d4;">🔍</span>
                    <h3 class="modal-title" style="font-size: 13px; margin: 0; font-weight: normal; color: #333;">敏感词文本检查</h3>
                </div>
                <div style="display: flex; gap: 4px;">
                    <button class="modal-close" onclick="closeQuickCheckModal()" style="width: 16px; height: 16px; border: none; background: #ddd; border-radius: 2px; font-size: 10px; color: #666; cursor: pointer;">&times;</button>
                </div>
            </div>
            <div class="modal-body" style="padding: 12px; background: white; height: calc(100% - 80px); display: flex; flex-direction: column;">

                <!-- 检查结果 -->
                <div id="quickCheckResults" style="flex: 1; overflow-y: auto; background: white; border: none; padding: 0; scrollbar-width: none; -ms-overflow-style: none;">
                    <style>
                        #quickCheckResults::-webkit-scrollbar {
                            display: none;
                        }
                    </style>
                    <div style="text-align: center; color: #6c757d; padding: 40px 20px;">
                        <div style="font-size: 32px; margin-bottom: 12px; opacity: 0.3;">🔍</div>
                        <div style="font-size: 12px;">正在检查敏感词...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 8px 12px; background: #f0f0f0; border-top: 1px solid #ddd; border-radius: 0 0 6px 6px; display: flex; justify-content: space-between; gap: 8px; height: 40px; align-items: center;">
                <button onclick="refreshSensitiveCheck()" style="padding: 4px 12px; border: 1px solid #0078d4; background: #0078d4; color: white; border-radius: 3px; cursor: pointer; font-size: 11px; height: 24px;" onmouseover="this.style.backgroundColor='#106ebe'" onmouseout="this.style.backgroundColor='#0078d4'">刷新</button>
                <button onclick="closeQuickCheckModal()" style="padding: 4px 12px; border: 1px solid #0078d4; background: #0078d4; color: white; border-radius: 3px; cursor: pointer; font-size: 11px; height: 24px;" onmouseover="this.style.backgroundColor='#106ebe'" onmouseout="this.style.backgroundColor='#0078d4'">关闭</button>
            </div>
        </div>
    </div>

    <!-- 拆分文本弹窗 -->
    <div id="splitTextModal" class="modal">
        <div class="modal-content" style="width: 500px; height: 480px;">
            <div class="modal-header" style="padding: 12px 20px;">
                <h3 class="modal-title" style="margin: 0; font-size: 16px;">✂️ 拆分文本</h3>
                <button class="modal-close" onclick="closeModal('splitTextModal')">&times;</button>
            </div>
            <div class="modal-body" style="display: flex; flex-direction: column; height: calc(100% - 80px); padding: 15px 20px;">
                <!-- 顶部信息栏：左侧页面指示器，右侧字符统计 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span id="pageIndicator" style="font-weight: 500; color: #007bff; font-size: 12px;">第1页 / 共2页</span>
                        <span style="color: #999; font-size: 12px;">|</span>
                        <label style="font-size: 12px; color: #666;">分成几页：</label>
                        <select id="modalSplitPages" onchange="autoSplitAndShow()"
                                style="padding: 4px 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px; width: 60px;">
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                        </select>
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        <span id="currentPageCharCount">0字符</span>
                    </div>
                </div>

                <!-- 当前页面内容 -->
                <div id="currentPageContent" style="flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 4px; background: white; overflow-y: auto; font-size: 12px; line-height: 1.1; font-weight: normal; letter-spacing: -0.3px; margin-bottom: 12px;">
                    <!-- 当前页面内容将显示在这里 -->
                </div>

                <!-- 底部按钮区域 -->
                <div style="background: #f8f9fa; padding: 10px 12px; border-radius: 4px; border: 1px solid #e9ecef;">
                    <!-- 按钮布局：左侧导航，右侧操作 -->
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <!-- 左侧：导航按钮 -->
                        <div style="display: flex; gap: 6px;">
                            <button id="prevPageBtn" class="btn" onclick="showPreviousPage()"
                                    style="padding: 6px 12px; font-size: 12px;" disabled>
                                ← 上一页
                            </button>
                            <button id="nextPageBtn" class="btn" onclick="showNextPage()"
                                    style="padding: 6px 12px; font-size: 12px;">
                                下一页 →
                            </button>
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div style="display: flex; gap: 6px;">
                            <button class="btn success" onclick="copyCurrentPage()"
                                    style="padding: 6px 12px; font-size: 12px;">
                                📋 复制当前页
                            </button>
                            <button class="btn" onclick="closeModal('splitTextModal')"
                                    style="background: #6c757d; border-color: #6c757d; color: white; padding: 6px 12px; font-size: 12px;">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 敏感词管理弹窗 -->
    <div id="sensitiveWordsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">🚫 敏感词管理</h3>
                <button class="modal-close" onclick="closeModal('sensitiveWordsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: grid; gap: 12px;">
                    <!-- 搜索敏感词 -->
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 6px;">
                        <h4 style="margin: 0 0 8px 0; color: #495057; font-size: 14px;">🔍 搜索敏感词</h4>
                        <div style="display: grid; grid-template-columns: 1fr 80px; gap: 8px; align-items: end;">
                            <input type="text" id="searchSensitiveWord" placeholder="输入关键词搜索" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;" oninput="searchSensitiveWords()">
                            <button class="btn primary" onclick="clearSensitiveSearch()" style="height: 30px; font-size: 12px; padding: 0 8px;">清除</button>
                        </div>
                    </div>

                    <!-- 添加敏感词 -->
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 6px; border: 1px solid #c3e6c3;">
                        <h4 style="margin: 0 0 8px 0; color: #495057; font-size: 14px;">➕ 添加敏感词</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 80px; gap: 8px; align-items: end;">
                            <input type="text" id="modalNewSensitiveWord" placeholder="敏感词" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;" onkeypress="if(event.key==='Enter') addModalSensitiveWord()">
                            <input type="text" id="modalNewReplacement" placeholder="替换词(可选)" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;" onkeypress="if(event.key==='Enter') addModalSensitiveWord()">
                            <button class="btn success" onclick="addModalSensitiveWord()" style="height: 30px; font-size: 12px; padding: 0 8px;">添加</button>
                        </div>
                        <div style="font-size: 11px; color: #666; margin-top: 6px;">
                            💡 不填替换词则只高亮显示
                        </div>
                    </div>

                    <!-- 敏感词列表 -->
                    <div>
                        <h4 style="margin: 0 0 8px 0; color: #495057; font-size: 14px;">📝 敏感词列表</h4>
                        <div id="modalSensitiveWordsList" style="max-height: 280px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 6px;">
                            <!-- 敏感词列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-info" style="font-size: 11px; color: #666;">
                    💡 支持导入/导出敏感词库
                </div>
                <div class="modal-footer-buttons">
                    <button class="btn success" onclick="manualSaveSensitiveWords()" style="font-size: 12px; padding: 6px 12px;">💾 保存</button>
                    <button class="btn" onclick="importSensitiveWords()" style="font-size: 12px; padding: 6px 12px;">📥 导入</button>
                    <button class="btn" onclick="exportSensitiveWords()" style="font-size: 12px; padding: 6px 12px;">📤 导出</button>
                    <button class="btn" onclick="closeModal('sensitiveWordsModal')" style="font-size: 12px; padding: 6px 12px;">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出Word弹窗 -->
    <div id="exportWordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">📄 导出Word文档</h3>
                <button class="modal-close" onclick="closeModal('exportWordModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">文件名：</label>
                        <input type="text" class="function-input" id="fileName" placeholder="输入文件名（不含扩展名）" style="width: 100%;">
                    </div>

                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">格式选项：</label>
                        <div class="export-option-group">
                            <label class="export-checkbox-label">
                                <input type="checkbox" id="preserveFormat" checked> 保持格式
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-info">
                    💡 支持保持原有格式的Word文档导出，按回车键快速生成高亮版本
                </div>
                <div class="modal-footer-buttons">
                    <button class="btn success" onclick="exportToWordAndClose()">📄 生成带高亮Word</button>
                    <button class="btn" onclick="generatePureWordDoc()">📄 生成纯净Word</button>
                    <button class="btn" onclick="closeModal('exportWordModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JSZip库用于ZIP文件生成 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</body>
</html>
