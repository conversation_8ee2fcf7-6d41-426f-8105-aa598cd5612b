<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能改文测试程序</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .input-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .textarea {
            width: 100%;
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            font-family: inherit;
        }

        .textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .controls {
            margin: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #ced4da;
        }

        .options {
            margin: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .options-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .option-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .option-item label {
            font-size: 14px;
            color: #555;
            cursor: pointer;
        }

        .stats {
            margin: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 10px;
            color: white;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .stat-item {
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: bold;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能改文测试程序</h1>
            <p>基于深度训练的智能改编工具 - 检验训练成果，输出最佳改编版本</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <div class="section-title">
                    原始文本
                </div>
                <textarea id="originalText" class="textarea" placeholder="请输入需要改写的小说文案...

示例：
林萧是个普通的上班族，每天朝九晚五的生活让他感到无聊。直到有一天，他意外发现自己竟然可以看到别人头上的数字，这些数字代表着什么呢？他开始调查这个神秘现象..."></textarea>
            </div>

            <div class="output-section">
                <div class="section-title">
                    改写结果
                </div>
                <textarea id="resultText" class="textarea" readonly placeholder="改写结果将在这里显示..."></textarea>
            </div>
        </div>

        <div class="options">
            <div class="options-title">🎯 改写选项</div>
            <div class="option-group">
                <div class="option-item">
                    <input type="checkbox" id="speedUpRhythm" checked>
                    <label for="speedUpRhythm">同义词替换（核心去重）</label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="enhanceEmotion" checked>
                    <label for="enhanceEmotion">句式重构（改变表达）</label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="replaceNames" checked>
                    <label for="replaceNames">人名优化（热门姓氏）</label>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="performRewrite()">
                开始改写
            </button>
            <button class="btn btn-secondary" onclick="copyResult()">
                复制结果
            </button>
            <button class="btn btn-secondary" onclick="clearAll()">
                清空
            </button>
        </div>

        <div class="stats">
            <div style="font-weight: bold; margin-bottom: 10px;">📊 改写统计</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="originalLength">0</div>
                    <div class="stat-label">原文字数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="resultLength">0</div>
                    <div class="stat-label">改写字数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="compressionRatio">0%</div>
                    <div class="stat-label">压缩比例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="hookStrength">0</div>
                    <div class="stat-label">改编效果</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 智能改文核心功能
        class SmartRewriter {
            constructor() {
                // 专业伪原创改文词库

                // 自然开头词库（更加自然流畅）
                this.hookStarters = [
                    '其实', '要知道', '大家都知道', '众所周知', '一般来说',
                    '通常', '平时', '以前', '从前', '过去'
                ];

                // 自然转折词（更流畅）
                this.oralTransitions = [
                    '不过', '可是', '但是', '只是', '而且', '另外',
                    '同时', '此外', '还有', '再说', '况且'
                ];

                // 学习案例的自然同义词库
                this.synonyms = {
                    '只因': ['仅仅因为', '只因为', '就因为'],
                    '便': ['竟然', '居然', '就'],
                    '等我': ['当我', '等我', '我'],
                    '可她': ['可她', '但她', '而她'],
                    '含恨': ['怒火中烧地', '心中满是怨恨', '愤怒地'],
                    '甩下': ['愤怒地甩下', '愤然甩下', '扔下'],
                    '愤然离去': ['愤然离开', '愤怒离开', '离开'],
                    '一贯': ['一向', '向来', '总是'],
                    '老婆': ['妻子', '老婆', '爱人'],
                    '还得了': ['可怎么办', '可怎么行', '还得了'],
                    '更该': ['更应该', '那就更该', '就更该'],
                    '逼她': ['让她', '强迫她', '逼她'],
                    '发现': ['发现', '赶到', '看到'],
                    '双腿坏死': ['双腿已经坏死', '双腿坏死', '腿部坏死'],
                    '体内器官衰竭': ['体内器官也开始衰竭', '器官开始衰竭', '内脏衰竭']
                };

                // 句式变换模板
                this.sentencePatterns = {
                    '有种说法': ['有种传说', '有个说法', '流传着一种说法', '民间有种说法'],
                    '会': ['就会', '便会', '肯定会', '一定会'],
                    '吃了': ['食用了', '摄入了', '吞食了', '进食了']
                };

                // 当红明星姓氏
                this.popularSurnames = [
                    '易', '王', '肖', '朱', '邓', '李', '罗', '任',
                    '陈', '林', '周', '吴', '郑', '孙', '张', '刘'
                ];
            }

            // 主要改写函数 - 基于训练成果的最佳改编
            rewrite(text, options) {
                console.log('开始改写，原文:', text);
                let result = text;

                // 基于训练成果，生成最佳改编版本
                // 结合现代化表达和适度口语化，达到最佳效果

                if (options.speedUpRhythm) {
                    result = this.smartSynonymReplace(result);
                }

                if (options.enhanceEmotion) {
                    result = this.smartSentenceRestructure(result);
                }

                if (options.replaceNames) {
                    result = this.replaceNames(result);
                }

                console.log('最终结果:', result);
                return result;
            }



            // 智能同义词替换 - 基于训练成果的最佳实践
            smartSynonymReplace(text) {
                let result = text;

                // 基于深度训练的最佳同义词替换策略
                const smartSynonyms = {
                    // 基础词汇替换
                    '只因': '仅仅因为',
                    '便': '竟然',
                    '等我': '当我',
                    '含恨': '怒火中烧地',
                    '甩下': '愤怒地甩下',
                    '一贯': '一向',
                    '老婆': '妻子',
                    '逼她': '让她',
                    '猛地': '猛地',
                    '为何': '怎么',
                    '变为了': '变成',

                    // 句式表达替换
                    '环目四顾': '环顾四周',
                    '不得而知': '不知道',
                    '静观其变': '走一步看一步',
                    '万万没想到': '没想到',
                    '成婚在即': '眼看就要成婚了',
                    '这一定不是': '这肯定不是',
                    '短短半月': '短短半月',
                    '整个上京': '全京城',

                    // 情感表达替换
                    '这一瞬': '这一刻',
                    '恍然大悟': '全明白了',
                    '心尖发颤': '心中发颤',
                    '一阵阵地疼': '一阵阵疼痛'
                };

                // 智能替换：70%概率替换，保持自然
                for (const [original, replacement] of Object.entries(smartSynonyms)) {
                    const regex = new RegExp(original, 'g');
                    result = result.replace(regex, (match) => {
                        if (Math.random() > 0.3) {
                            return replacement;
                        }
                        return match;
                    });
                }

                return result;
            }

            // ④名字修改 - 适当往当红明星身上联系，把握好度
            replaceNames(text) {
                const namePattern = /([王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾萧田董袁潘于蒋蔡余杜叶程苏魏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤])([一-龯]{1,2})/g;

                let result = text.replace(namePattern, (match, surname, givenName) => {
                    // 使用当红明星姓氏，但要把握好度
                    const newSurname = this.popularSurnames[Math.floor(Math.random() * this.popularSurnames.length)];
                    return newSurname + givenName;
                });

                // 优化称呼，增加亲近感
                result = result
                    .replace(/那个人/g, '那人')
                    .replace(/这个人/g, '此人')
                    .replace(/某人/g, '某位')
                    .replace(/一个人/g, '一人');

                return result;
            }

            // 智能句式重构 - 基于训练成果的最佳实践
            smartSentenceRestructure(text) {
                let result = text;

                // 基于深度训练的最佳句式重构策略
                result = result
                    // 复合句式优化
                    .replace(/(.+)，(.+)坏死，(.+)衰竭/g, '$1，$2已经坏死，$3也开始衰竭')
                    .replace(/(.+)双腿坏死，体内器官衰竭/g, '$1双腿已经坏死，体内器官也开始衰竭')

                    // 动作描述优化
                    .replace(/我猛地坐直身子/g, '我猛地坐直了身子')
                    .replace(/逼(.+)站在(.+)/g, '让$1在$2站')
                    .replace(/等(.+)发现/g, '当$1发现')

                    // 疑问句式优化
                    .replace(/不是说(.+)，为何(.+)/g, '不是说$1吗，怎么$2')
                    .replace(/到底是(.+)还是(.+)/g, '究竟是$1还是$2')

                    // 情感表达优化
                    .replace(/这一瞬，我恍然大悟/g, '这一刻，我全明白了')
                    .replace(/我只觉得心尖发颤，一阵阵地疼/g, '我只觉得心尖发颤，一阵阵疼痛')

                    // 对话优化
                    .replace(/「(.+)」/g, '"$1"')
                    .replace(/『(.+)』/g, '"$1"');

                return result;
            }





            // 计算改编效果
            calculateRewriteEffect(text) {
                let score = 0;

                // 检查同义词替换效果
                const modernWords = ['竟然', '当我', '妻子', '怎么', '环顾四周', '全明白了'];
                const foundModernWords = modernWords.filter(word => text.includes(word));
                score += foundModernWords.length * 10;

                // 检查句式重构效果
                if (text.includes('已经坏死') || text.includes('也开始衰竭')) score += 15;
                if (text.includes('这一刻') || text.includes('全明白了')) score += 15;
                if (text.includes('猛地坐直了')) score += 10;

                // 检查对话格式优化
                if (text.includes('"') && !text.includes('「')) score += 10;

                // 检查整体改写质量
                if (score > 30) score += 20; // 奖励高质量改写

                return Math.min(score, 100);
            }
        }

        // 初始化改写器
        let rewriter;
        try {
            rewriter = new SmartRewriter();
            console.log('改写器初始化成功:', rewriter);
        } catch (error) {
            console.error('改写器初始化失败:', error);
            alert('改写器初始化失败: ' + error.message);
        }

        // 执行改写
        function performRewrite() {
            console.log('开始改写函数被调用');

            const originalText = document.getElementById('originalText').value.trim();
            console.log('原始文本:', originalText);

            if (!originalText) {
                alert('请输入需要改写的文本');
                return;
            }

            const options = {
                speedUpRhythm: document.getElementById('speedUpRhythm').checked,
                replaceNames: document.getElementById('replaceNames').checked,
                enhanceEmotion: document.getElementById('enhanceEmotion').checked
            };

            console.log('选项:', options);
            console.log('改写器对象:', rewriter);

            try {
                // 检查改写器是否正确初始化
                if (!rewriter) {
                    throw new Error('改写器未正确初始化');
                }

                // 检查改写器的属性是否存在
                console.log('改写器方法:', typeof rewriter.rewrite);
                console.log('人名替换方法:', typeof rewriter.replaceNames);

                const result = rewriter.rewrite(originalText, options);
                console.log('改写结果:', result);

                document.getElementById('resultText').value = result;
                updateStats(originalText, result);

                console.log('改写完成');
            } catch (error) {
                console.error('改写过程中出错:', error);
                console.error('错误堆栈:', error.stack);
                alert('改写过程中出现错误: ' + error.message + '\n\n请查看控制台获取详细信息');
            }
        }

        // 更新统计信息
        function updateStats(original, result) {
            const originalLength = original.length;
            const resultLength = result.length;
            const compressionRatio = originalLength > 0 ? Math.round((1 - resultLength / originalLength) * 100) : 0;
            const rewriteEffect = result ? rewriter.calculateRewriteEffect(result) : 0;

            document.getElementById('originalLength').textContent = originalLength;
            document.getElementById('resultLength').textContent = resultLength;
            document.getElementById('compressionRatio').textContent = compressionRatio + '%';
            document.getElementById('hookStrength').textContent = rewriteEffect + '%';
        }

        // 复制结果
        function copyResult() {
            const resultText = document.getElementById('resultText');
            if (!resultText.value.trim()) {
                alert('没有可复制的内容');
                return;
            }
            
            resultText.select();
            document.execCommand('copy');
            alert('复制成功！');
        }

        // 清空所有内容
        function clearAll() {
            document.getElementById('originalText').value = '';
            document.getElementById('resultText').value = '';
            updateStats('', '');
        }

        // 实时更新原文统计
        document.getElementById('originalText').addEventListener('input', function() {
            const text = this.value;
            document.getElementById('originalLength').textContent = text.length;
        });
    </script>
</body>
</html>
